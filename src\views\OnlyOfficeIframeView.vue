<template>
  <div class="onlyoffice-iframe-view">
    <div class="header">
      <h1>OnlyOffice Spreadsheet (iframe方式)</h1>
      <div class="controls">
        <el-button type="primary" @click="refreshEditor">刷新编辑器</el-button>
        <el-button type="success" @click="openInNewTab">新窗口打开</el-button>
        <el-button type="info" @click="toggleFullscreen">{{ isFullscreen ? '退出全屏' : '全屏显示' }}</el-button>
        <el-button type="warning" @click="showHelp">使用说明</el-button>
      </div>
    </div>
    
    <div class="editor-container" :class="{ 'fullscreen': isFullscreen }">
      <div class="loading-overlay" v-if="isLoading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>正在加载OnlyOffice编辑器...</span>
      </div>
      
      <iframe
        ref="editorFrame"
        :src="editorUrl"
        frameborder="0"
        width="100%"
        height="100%"
        @load="onEditorLoad"
        @error="onEditorError"
      ></iframe>
    </div>
    
    <div class="status-bar" v-if="!isFullscreen">
      <span>状态: {{ editorStatus }}</span>
      <span>编辑器URL: {{ editorUrl }}</span>
    </div>
    
    <!-- 使用说明对话框 -->
    <el-dialog v-model="helpDialogVisible" title="OnlyOffice使用说明" width="600px">
      <div class="help-content">
        <h3>功能特点</h3>
        <ul>
          <li>完整的Excel兼容性，支持公式、图表、数据透视表等</li>
          <li>实时协作编辑，多人同时编辑同一文档</li>
          <li>支持导入/导出多种格式：xlsx, xls, ods, csv等</li>
          <li>丰富的格式化选项和数据处理功能</li>
          <li>内置函数库，支持复杂的数据计算</li>
        </ul>
        
        <h3>快捷键</h3>
        <ul>
          <li><kbd>Ctrl + S</kbd> - 保存文档</li>
          <li><kbd>Ctrl + Z</kbd> - 撤销</li>
          <li><kbd>Ctrl + Y</kbd> - 重做</li>
          <li><kbd>Ctrl + C/V</kbd> - 复制/粘贴</li>
          <li><kbd>F11</kbd> - 全屏模式</li>
        </ul>
        
        <h3>注意事项</h3>
        <ul>
          <li>首次加载可能需要一些时间，请耐心等待</li>
          <li>建议使用现代浏览器以获得最佳体验</li>
          <li>如遇到加载问题，请尝试刷新页面</li>
        </ul>
      </div>
      
      <template #footer>
        <el-button @click="helpDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'

// 响应式数据
const editorFrame = ref(null)
const editorStatus = ref('初始化中...')
const isLoading = ref(true)
const isFullscreen = ref(false)
const helpDialogVisible = ref(false)

// OnlyOffice编辑器URL
const editorUrl = ref('/web-apps/apps/spreadsheeteditor/main/index.html')

// 刷新编辑器
const refreshEditor = () => {
  if (editorFrame.value) {
    isLoading.value = true
    editorStatus.value = '重新加载中...'
    editorFrame.value.src = editorUrl.value + '?t=' + Date.now()
  }
}

// 在新窗口打开
const openInNewTab = () => {
  window.open(editorUrl.value, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes')
  ElMessage.success('已在新窗口打开OnlyOffice编辑器')
}

// 切换全屏
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  if (isFullscreen.value) {
    document.documentElement.requestFullscreen?.()
  } else {
    document.exitFullscreen?.()
  }
}

// 显示帮助
const showHelp = () => {
  helpDialogVisible.value = true
}

// 编辑器加载完成
const onEditorLoad = () => {
  isLoading.value = false
  editorStatus.value = '编辑器已就绪'
  ElMessage.success('OnlyOffice编辑器加载成功')
}

// 编辑器加载错误
const onEditorError = () => {
  isLoading.value = false
  editorStatus.value = '加载失败'
  ElMessage.error('OnlyOffice编辑器加载失败，请检查文件路径')
}

// 监听全屏变化
const handleFullscreenChange = () => {
  if (!document.fullscreenElement) {
    isFullscreen.value = false
  }
}

// 组件挂载
onMounted(() => {
  editorStatus.value = '正在加载编辑器...'
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  
  // 设置一个超时，如果长时间没有加载完成则提示
  setTimeout(() => {
    if (isLoading.value) {
      ElMessage.warning('编辑器加载时间较长，请检查网络连接或文件路径')
    }
  }, 10000)
})

// 组件卸载
onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
  if (isFullscreen.value) {
    document.exitFullscreen?.()
  }
})
</script>

<style scoped>
.onlyoffice-iframe-view {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.header {
  background: white;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  z-index: 1000;
}

.header h1 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 24px;
}

.controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.editor-container {
  flex: 1;
  position: relative;
  padding: 20px;
  overflow: hidden;
}

.editor-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  padding: 0;
  z-index: 9999;
  background: white;
}

.editor-container iframe {
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.editor-container.fullscreen iframe {
  border: none;
  border-radius: 0;
  box-shadow: none;
}

.loading-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  font-size: 16px;
  color: #666;
  z-index: 10;
}

.loading-overlay .el-icon {
  font-size: 32px;
}

.status-bar {
  background: #f8f9fa;
  padding: 10px 20px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #666;
}

.help-content h3 {
  color: #333;
  margin: 20px 0 10px 0;
}

.help-content h3:first-child {
  margin-top: 0;
}

.help-content ul {
  margin: 0 0 15px 0;
  padding-left: 20px;
}

.help-content li {
  margin-bottom: 5px;
  line-height: 1.5;
}

.help-content kbd {
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 3px;
  padding: 2px 6px;
  font-family: monospace;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    padding: 15px;
  }
  
  .header h1 {
    font-size: 20px;
    margin-bottom: 10px;
  }
  
  .controls {
    flex-direction: column;
  }
  
  .editor-container {
    padding: 10px;
  }
  
  .status-bar {
    flex-direction: column;
    gap: 5px;
  }
}
</style>
