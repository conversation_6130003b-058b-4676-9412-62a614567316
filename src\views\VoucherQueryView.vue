<template>
  <div class="voucher-query-view">
    <div class="section-header">
      <h2>凭证查询系统</h2>
      <p>可视化复杂类别组成的凭证查询工具</p>
    </div>

    <!-- 查询面板容器 -->
    <div class="query-panels-container">
      <div class="panels-header">
        <h3>查询面板</h3>
        <div class="panel-controls">
          <el-button type="primary" @click="addQueryPanel" :icon="Plus">
            添加面板
          </el-button>
          <el-button type="success" @click="loadSampleQuery" :icon="Search">
            加载示例查询
          </el-button>
          <el-tag type="info" size="large">
            每个面板独立查询，后台自动计算交集
          </el-tag>
        </div>
      </div>

      <!-- 查询面板列表 -->
      <div class="query-panels">
        <div v-for="(panel, index) in queryPanels" :key="panel.id" class="query-panel">
          <div class="panel-header">
            <span class="panel-title">查询面板 {{ index + 1 }}</span>
            <div class="panel-actions">
              <el-button type="primary" size="small" @click="addCondition(panel.id)" :icon="Plus">
                添加条件
              </el-button>
              <el-button type="danger" size="small" @click="removePanel(panel.id)" :icon="Delete"
                v-if="queryPanels.length > 1">
                删除面板
              </el-button>
            </div>
          </div>

          <!-- 条件列表 -->
          <div class="conditions-container">
            <div v-for="(condition, condIndex) in panel.conditions" :key="condition.id" class="condition-row">
              <!-- 逻辑连接符 -->
              <div class="logic-operator" v-if="condIndex > 0">
                <el-select v-model="condition.logic" placeholder="逻辑">
                  <el-option label="AND" value="AND" />
                  <el-option label="OR" value="OR" />
                </el-select>
              </div>

              <!-- 字段选择 -->
              <div class="field-select">
                <el-select v-model="condition.field" placeholder="选择字段" @change="onFieldChange(condition)">
                  <el-option v-for="field in queryFields" :key="field.value" :label="field.label"
                    :value="field.value" />
                </el-select>
              </div>

              <!-- 操作符选择 -->
              <div class="operator-select">
                <el-select v-model="condition.operator" placeholder="操作符" @change="onOperatorChange(condition)">
                  <el-option v-for="op in getOperatorsForField(condition.field)" :key="op.value" :label="op.label"
                    :value="op.value" />
                </el-select>
              </div>

              <!-- 值输入 -->
              <div class="value-input">
                <!-- 日期范围输入 -->
                <div v-if="getFieldType(condition.field) === 'daterange'" class="range-input">
                  <el-date-picker v-model="condition.startDate" type="date" placeholder="开始日期" format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled="condition.operator === 'is_null' || condition.operator === 'is_not_null'" />
                  <span class="range-separator">至</span>
                  <el-date-picker v-model="condition.endDate" type="date" placeholder="结束日期" format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled="condition.operator === 'is_null' || condition.operator === 'is_not_null'" />
                </div>

                <!-- 金额范围输入 -->
                <div v-else-if="getFieldType(condition.field) === 'numberrange'" class="range-input">
                  <el-input-number v-model="condition.minAmount" placeholder="最小金额" :precision="2" :step="0.01"
                    :disabled="condition.operator === 'is_null' || condition.operator === 'is_not_null'" />
                  <span class="range-separator">至</span>
                  <el-input-number v-model="condition.maxAmount" placeholder="最大金额" :precision="2" :step="0.01"
                    :disabled="condition.operator === 'is_null' || condition.operator === 'is_not_null'" />
                </div>

                <!-- 文本输入（支持多值） -->
                <div v-else-if="getFieldType(condition.field) === 'text'" class="text-input">
                  <el-input v-model="condition.value" :placeholder="getPlaceholder(condition)"
                    :disabled="condition.operator === 'is_null' || condition.operator === 'is_not_null'" type="textarea"
                    :rows="2" />
                  <div class="input-hint">支持用逗号(,)或中文逗号(，)分隔多个值</div>
                </div>

                <!-- 其他类型输入 -->
                <component v-else :is="getInputComponent(condition)" v-model="condition.value"
                  :placeholder="getPlaceholder(condition)"
                  :disabled="condition.operator === 'is_null' || condition.operator === 'is_not_null'"
                  v-bind="getInputProps(condition)" />
              </div>

              <!-- 删除条件按钮 -->
              <div class="condition-actions">
                <el-button type="danger" size="small" @click="removeCondition(panel.id, condition.id)" :icon="Delete"
                  v-if="panel.conditions.length > 1" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-buttons">
      <el-button type="primary" size="large" @click="executeQuery" :loading="loading" :icon="Search">
        多面板交集查询
      </el-button>
      <el-button type="success" size="large" @click="showPanelSelectionDialog" :loading="exportLoading" :icon="Download">
        单面板查询
      </el-button>
      <el-button type="info" size="large" @click="showGeneratedSQL" :icon="Document">
        查看SQL
      </el-button>
    </div>
    
    <!-- 面板选择对话框 -->
    <el-dialog v-model="showPanelDialog" title="选择查询面板" width="500px">
      <div class="panel-selection">
        <p class="panel-selection-hint">请选择要查询的面板：</p>
        <el-radio-group v-model="selectedPanelIndex">
          <div v-for="(panel, index) in queryPanels" :key="panel.id" class="panel-selection-item">
            <el-radio :label="index">查询面板 {{ index + 1 }}</el-radio>
          </div>
        </el-radio-group>
      </div>
      <template #footer>
        <el-button @click="showPanelDialog = false">取消</el-button>
        <el-button type="primary" @click="exportSelectedPanel" :loading="exportLoading">确认查询</el-button>
      </template>
    </el-dialog>

    <!-- SQL预览对话框 -->
    <el-dialog v-model="showSQLDialog" title="生成的SQL语句" width="80%">
      <div class="sql-preview">
        <pre>{{ generatedSQL }}</pre>
      </div>
      <template #footer>
        <el-button @click="showSQLDialog = false">关闭</el-button>
        <el-button type="primary" @click="copySQLToClipboard">复制SQL</el-button>
      </template>
    </el-dialog>

    <!-- 查询结果展示 -->
    <div class="results-container" v-if="hasResults">
      <div class="results-header">
        <h3>查询结果</h3>
        <span class="result-count">共找到 {{ resultCount }} 条记录</span>
      </div>

      <div class="table-container">
        <VTableComponent :data="formattedTableData" :height="500" :width="1200" @cell-click="handleCellClick" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Delete, Search, Download } from '@element-plus/icons-vue'
import VTableComponent from '@/components/VTableComponent.vue'

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const hasResults = ref(false)
const resultCount = ref(0)
// 移除panelLogic变量，不再需要面板间逻辑选择
const queryPanels = ref([])
const tableData = ref([])
const tableColumns = ref([])
const showSQLDialog = ref(false)
const generatedSQL = ref('')
// 面板选择对话框相关
const showPanelDialog = ref(false)
const selectedPanelIndex = ref(0)

// 格式化表格数据为二维数组
const formattedTableData = computed(() => {
  if (tableData.value.length === 0) return []

  // 表头
  const headers = tableColumns.value.map(col => col.title)

  // 数据行
  const rows = tableData.value.map(row => {
    return tableColumns.value.map(col => row[col.field] || '')
  })

  return [headers, ...rows]
})

// 查询字段配置
const queryFields = ref([
  { label: '日期范围', value: '过帐日期', type: 'daterange' },
  { label: '金额范围', value: '带符号的本位币金额', type: 'numberrange' },
  { label: '事由', value: '文本', type: 'text' },
  { label: '总账科目长文本', value: '总账科目长文本', type: 'text' },
  { label: '合同编号', value: '合同', type: 'text' },
  { label: '合同名称', value: '合同文本描述', type: 'text' },
  { label: '项目名称', value: 'WBS元素描述', type: 'text' },
  { label: '凭证编号', value: '凭证编号', type: 'text' },
  { label: '中台单据号', value: '中台单据号', type: 'text' },
  { label: '客户名称', value: '客户描述', type: 'text' },
  { label: '客户编码', value: '客户', type: 'text' },
  { label: '供应商名称', value: '供应商描述', type: 'text' },
  { label: '供应商编码', value: '供应商', type: 'text' }
])

// 操作符配置
const operators = {
  text: [
    { label: '精准匹配', value: 'exact' },
    { label: '模糊匹配', value: 'fuzzy' },
    { label: '为空', value: 'is_null' },
    { label: '不为空', value: 'is_not_null' }
  ],
  daterange: [
    { label: '在范围内', value: 'between' },
    { label: '为空', value: 'is_null' },
    { label: '不为空', value: 'is_not_null' }
  ],
  numberrange: [
    { label: '在范围内', value: 'between' },
    { label: '为空', value: 'is_null' },
    { label: '不为空', value: 'is_not_null' }
  ]
}

// 生成唯一ID
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substring(2)
}

// 创建新的查询面板
function createNewPanel() {
  return {
    id: generateId(),
    conditions: [createNewCondition()]
  }
}

// 创建新的查询条件
function createNewCondition() {
  return {
    id: generateId(),
    field: '',
    operator: '',
    value: '',
    startDate: '',
    endDate: '',
    minAmount: null,
    maxAmount: null,
    logic: 'AND'
  }
}

// 添加查询面板
function addQueryPanel() {
  queryPanels.value.push(createNewPanel())
}

// 删除查询面板
function removePanel(panelId) {
  const index = queryPanels.value.findIndex(panel => panel.id === panelId)
  if (index > -1) {
    queryPanels.value.splice(index, 1)
  }
}

// 添加查询条件
function addCondition(panelId) {
  const panel = queryPanels.value.find(p => p.id === panelId)
  if (panel) {
    panel.conditions.push(createNewCondition())
  }
}

// 删除查询条件
function removeCondition(panelId, conditionId) {
  const panel = queryPanels.value.find(p => p.id === panelId)
  if (panel) {
    const index = panel.conditions.findIndex(c => c.id === conditionId)
    if (index > -1) {
      panel.conditions.splice(index, 1)
    }
  }
}

// 获取字段类型
function getFieldType(fieldValue) {
  const field = queryFields.value.find(f => f.value === fieldValue)
  return field ? field.type : 'text'
}

// 获取字段对应的操作符
function getOperatorsForField(fieldValue) {
  const field = queryFields.value.find(f => f.value === fieldValue)
  return field ? operators[field.type] || [] : []
}

// 字段变化处理
function onFieldChange(condition) {
  condition.operator = ''
  condition.value = ''
  condition.startDate = ''
  condition.endDate = ''
  condition.minAmount = null
  condition.maxAmount = null
}

// 操作符变化处理
function onOperatorChange(condition) {
  // 如果选择了"为空"或"不为空"，清空值
  if (condition.operator === 'is_null' || condition.operator === 'is_not_null') {
    condition.value = ''
    condition.startDate = ''
    condition.endDate = ''
    condition.minAmount = null
    condition.maxAmount = null
  }
}

// 获取输入组件
function getInputComponent(condition) {
  const field = queryFields.value.find(f => f.value === condition.field)
  if (!field) return 'el-input'

  switch (field.type) {
    case 'date':
      return 'el-date-picker'
    case 'number':
      return 'el-input-number'
    default:
      return 'el-input'
  }
}

// 获取输入组件属性
function getInputProps(condition) {
  const field = queryFields.value.find(f => f.value === condition.field)
  if (!field) return {}

  switch (field.type) {
    case 'date':
      return {
        type: 'date',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD'
      }
    case 'number':
      return {
        precision: 2,
        step: 0.01
      }
    default:
      return {}
  }
}

// 获取占位符文本
function getPlaceholder(condition) {
  const field = queryFields.value.find(f => f.value === condition.field)
  if (!field) return '请输入值'

  if (condition.operator === 'is_null' || condition.operator === 'is_not_null') {
    return '无需输入值'
  }

  if (condition.operator === 'exact') {
    return `请输入精准的${field.label}，多个值用逗号分隔`
  }

  if (condition.operator === 'fuzzy') {
    return `请输入模糊匹配的${field.label}，多个值用逗号分隔`
  }

  return `请输入${field.label}`
}

// 执行查询
async function executeQuery() {
  // 验证查询条件
  if (!validateQuery()) {
    return
  }

  loading.value = true

  try {
    // 为每个面板生成独立的SQL语句
    const panelSQLs = generatePanelSQLs()
    if (!panelSQLs || panelSQLs.length === 0) {
      loading.value = false
      return
    }

    console.log('生成的面板SQL语句:', panelSQLs)

    // 构建查询参数
    const queryParams = {
      panelSQLs: panelSQLs,
      panels: queryPanels.value
    }

    // 调用实际的API
    try {
      const response = await fetch('http://localhost:8000/api/voucher-query', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(queryParams)
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      if (result.success && result.data && result.data.length > 0) {
        // API返回的数据格式：[headers, ...rows]
        const headers = result.data[0]
        const rows = result.data.slice(1)

        // 转换为对象数组格式
        const objectData = rows.map(row => {
          const obj = {}
          headers.forEach((header, index) => {
            obj[header] = row[index]
          })
          return obj
        })

        tableData.value = objectData
        tableColumns.value = getVoucherColumns()
        resultCount.value = objectData.length
        hasResults.value = true

        ElMessage.success(`查询完成，共找到 ${objectData.length} 条记录`)
      } else {
        throw new Error(result.message || '查询返回空结果')
      }
    } catch (apiError) {
      console.warn('API调用失败，使用模拟数据:', apiError)

      // API失败时使用模拟数据
      const mockData = generateMockVoucherData()
      tableData.value = mockData
      tableColumns.value = getVoucherColumns()
      resultCount.value = mockData.length
      hasResults.value = true

      ElMessage.success(`查询完成（模拟数据），共找到 ${mockData.length} 条记录`)
    }

  } catch (error) {
    console.error('查询失败:', error)
    ElMessage.error('查询失败，请重试')
  } finally {
    loading.value = false
  }
}

// 验证查询条件
function validateQuery() {
  for (const panel of queryPanels.value) {
    for (const condition of panel.conditions) {
      if (!condition.field) {
        ElMessage.warning('请选择查询字段')
        return false
      }
      if (!condition.operator) {
        ElMessage.warning('请选择操作符')
        return false
      }

      // 跳过"为空"和"不为空"的验证
      if (condition.operator === 'is_null' || condition.operator === 'is_not_null') {
        continue
      }

      const fieldType = getFieldType(condition.field)

      // 验证日期范围
      if (fieldType === 'daterange') {
        if (!condition.startDate || !condition.endDate) {
          ElMessage.warning('请输入完整的日期范围')
          return false
        }
      }
      // 验证金额范围
      else if (fieldType === 'numberrange') {
        if (condition.minAmount === null || condition.maxAmount === null) {
          ElMessage.warning('请输入完整的金额范围')
          return false
        }
      }
      // 验证文本字段
      else if (fieldType === 'text') {
        if (!condition.value || condition.value.trim() === '') {
          ElMessage.warning('请输入查询值')
          return false
        }
      }
    }
  }
  return true
}

// 为每个面板生成独立的SQL语句
function generatePanelSQLs() {
  if (!validateQuery()) {
    return null
  }

  const tableName = '明细帐'
  const panelSQLs = []

  for (let panelIndex = 0; panelIndex < queryPanels.value.length; panelIndex++) {
    const panel = queryPanels.value[panelIndex]
    const conditionClauses = []

    for (const condition of panel.conditions) {
      const fieldType = getFieldType(condition.field)
      let clause = ''

      // 根据字段类型和操作符生成SQL条件
      if (condition.operator === 'is_null') {
        clause = `${condition.field} IS NULL`
      } else if (condition.operator === 'is_not_null') {
        clause = `${condition.field} IS NOT NULL`
      } else if (fieldType === 'daterange' && condition.operator === 'between') {
        clause = `${condition.field} BETWEEN '${condition.startDate}' AND '${condition.endDate}'`
      } else if (fieldType === 'numberrange' && condition.operator === 'between') {
        clause = `${condition.field} BETWEEN ${condition.minAmount} AND ${condition.maxAmount}`
      } else if (fieldType === 'text') {
        const values = condition.value.split(/[,，]/).map(v => v.trim()).filter(v => v)
        if (values.length > 0) {
          if (condition.operator === 'exact') {
            // 精准匹配：使用IN操作符
            const quotedValues = values.map(v => `'${v.replace(/'/g, "''")}'`).join(', ')
            clause = `${condition.field} IN (${quotedValues})`
          } else if (condition.operator === 'fuzzy') {
            // 模糊匹配：使用LIKE操作符，多个值用OR连接
            const likeClauses = values.map(v => `${condition.field} LIKE '%${v.replace(/'/g, "''")}%'`)
            clause = `(${likeClauses.join(' OR ')})`
          }
        }
      }

      if (clause) {
        conditionClauses.push(clause)
      }
    }

    // 将面板内的条件用AND/OR连接
    if (conditionClauses.length > 0) {
      let panelClause = conditionClauses[0]
      for (let i = 1; i < conditionClauses.length; i++) {
        const logic = panel.conditions[i].logic || 'AND'
        panelClause += ` ${logic} ${conditionClauses[i]}`
      }

      const panelSQL = `SELECT * FROM ${tableName} WHERE ${panelClause}`
      panelSQLs.push({
        panelIndex: panelIndex,
        sql: panelSQL,
        description: `面板${panelIndex + 1}查询`
      })
    }
  }

  return panelSQLs
}

// 生成SQL查询语句（保留用于显示SQL功能）
function generateSQL() {
  const panelSQLs = generatePanelSQLs()
  if (!panelSQLs || panelSQLs.length === 0) {
    return null
  }

  // 将所有面板SQL组合显示
  return panelSQLs.map(item => `-- ${item.description}\n${item.sql}`).join('\n\n')
}

// 显示面板选择对话框
function showPanelSelectionDialog() {
  if (!validateQuery()) {
    return
  }
  
  if (queryPanels.value.length === 0) {
    ElMessage.warning('请先添加查询面板')
    return
  }
  
  // 默认选择第一个面板
  selectedPanelIndex.value = 0
  showPanelDialog.value = true
}

// 导出选中的面板数据
async function exportSelectedPanel() {
  if (selectedPanelIndex.value < 0 || selectedPanelIndex.value >= queryPanels.value.length) {
    ElMessage.warning('请选择有效的查询面板')
    return
  }
  
  exportLoading.value = true
  showPanelDialog.value = false

  try {
    // 获取面板SQL语句
    const panelSQLs = generatePanelSQLs()
    if (!panelSQLs || panelSQLs.length === 0) {
      ElMessage.warning('请先设置有效的查询条件')
      exportLoading.value = false
      return
    }

    // 只使用选中的面板SQL
    const selectedPanelSQL = panelSQLs.find(p => p.panelIndex === selectedPanelIndex.value)
    if (!selectedPanelSQL) {
      ElMessage.warning('所选面板没有有效的查询条件')
      exportLoading.value = false
      return
    }

    console.log('选中的面板SQL语句:', selectedPanelSQL)

    // 构建请求数据 - 只包含选中的面板
    const requestData = {
      panelSQLs: [selectedPanelSQL],
      panels: [{
        id: queryPanels.value[selectedPanelIndex.value].id,
        description: `面板 ${selectedPanelIndex.value + 1}`
      }]
    }

    // 调用后端API
    const response = await fetch('http://localhost:8000/api/voucher-query-single', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestData)
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.detail || '查询失败')
    }

    // 处理返回的数据
    const result = await response.json()

    // 更新查询结果数据
    if (result.data && result.data.length > 0) {
      // API返回的数据格式：[headers, ...rows]
      const headers = result.data[0]
      const rows = result.data.slice(1)

      // 转换为对象数组格式
      const objectData = rows.map(row => {
        const obj = {}
        headers.forEach((header, index) => {
          obj[header] = row[index]
        })
        return obj
      })

      tableData.value = objectData
      tableColumns.value = getVoucherColumns()
      resultCount.value = objectData.length
      hasResults.value = true

      ElMessage.success(`单面板查询完成，共找到 ${objectData.length} 条记录`)
    } else {
      ElMessage.warning('查询结果为空')
    }
  } catch (error) {
    console.error('查询失败:', error)
    ElMessage.error(`查询失败: ${error.message}`)
  } finally {
    exportLoading.value = false
  }
}

// 全凭证导出 (保留原函数，但不再使用)
async function exportAllVouchers() {
  exportLoading.value = true

  try {
    // 获取面板SQL语句
    const panelSQLs = generatePanelSQLs()
    if (!panelSQLs || panelSQLs.length === 0) {
      ElMessage.warning('请先设置有效的查询条件')
      exportLoading.value = false
      return
    }

    console.log('生成的面板SQL语句:', panelSQLs)

    // 构建请求数据
    const requestData = {
      panelSQLs: panelSQLs,
      panels: queryPanels.value.map(panel => ({
        id: panel.id,
        description: `面板 ${queryPanels.value.indexOf(panel) + 1}`
      }))
    }

    // 调用后端API
    const response = await fetch('http://localhost:8000/api/voucher-query', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestData)
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.detail || '查询失败')
    }

    // 处理返回的数据
    const result = await response.json()

    // 更新查询结果数据
    if (result.data && result.data.length > 0) {
      // API返回的数据格式：[headers, ...rows]
      const headers = result.data[0]
      const rows = result.data.slice(1)

      // 转换为对象数组格式
      const objectData = rows.map(row => {
        const obj = {}
        headers.forEach((header, index) => {
          obj[header] = row[index]
        })
        return obj
      })

      tableData.value = objectData
      tableColumns.value = getVoucherColumns()
      resultCount.value = objectData.length
      hasResults.value = true

      ElMessage.success(`查询完成，共找到 ${objectData.length} 条记录`)
    } else {
      ElMessage.warning('查询结果为空')
    }

    // 更新面板结果统计
    if (result.panel_results) {
      result.panel_results.forEach(panelResult => {
        const panelIndex = panelResult.panelIndex
        if (queryPanels.value[panelIndex]) {
          // 添加结果计数到面板对象
          queryPanels.value[panelIndex].resultCount = panelResult.count
        }
      })
    }
  } catch (error) {
    console.error('查询失败:', error)
    ElMessage.error(`查询失败: ${error.message}`)
  } finally {
    exportLoading.value = false
  }
}

// 生成模拟凭证数据
function generateMockVoucherData() {
  const data = []
  const reasons = ['材料采购', '工程款支付', '设备租赁', '人工费用', '管理费用', '财务费用', '销售费用', '其他费用']
  const accounts = ['原材料', '应付账款', '银行存款', '应收账款', '固定资产', '累计折旧', '主营业务收入', '主营业务成本']
  const customers = ['华为技术有限公司', '中国建筑股份有限公司', '中国石油天然气集团', '阿里巴巴集团', '腾讯控股有限公司']
  const suppliers = ['中国中铁股份有限公司', '中国建材集团', '宝钢集团', '中国石化', '中国移动通信集团']

  for (let i = 1; i <= 50; i++) {
    const randomReason = reasons[Math.floor(Math.random() * reasons.length)]
    const randomAccount = accounts[Math.floor(Math.random() * accounts.length)]
    const randomCustomer = customers[Math.floor(Math.random() * customers.length)]
    const randomSupplier = suppliers[Math.floor(Math.random() * suppliers.length)]

    data.push({
      id: i,
      voucher_number: `PZ${String(i).padStart(6, '0')}`,
      date: `2024-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
      reason: `${randomReason}-${i}`,
      account_text: `${randomAccount}-详细科目${i}`,
      contract_number: `HT${String(i).padStart(8, '0')}`,
      platform_doc_number: `ZT${String(i).padStart(10, '0')}`,
      customer_name: randomCustomer,
      customer_code: `C${String(i).padStart(6, '0')}`,
      supplier_name: randomSupplier,
      supplier_code: `S${String(i).padStart(6, '0')}`,
      amount: (Math.random() * 1000000).toFixed(2),
      debit_amount: (Math.random() * 500000).toFixed(2),
      credit_amount: (Math.random() * 500000).toFixed(2)
    })
  }
  return data
}

// 获取凭证表格列配置
function getVoucherColumns() {
  return [
    { field: '凭证编号', title: '凭证编号', width: 120 },
    { field: '过帐日期', title: '日期', width: 100 },
    { field: '文本', title: '事由', width: 150 },
    { field: '总账科目长文本', title: '总账科目长文本', width: 180 },
    { field: '合同编号', title: '合同编号', width: 130 },
    { field: '中台单据号', title: '中台单据号', width: 130 },
    { field: '客户描述', title: '客户名称', width: 150 },
    { field: '客户', title: '客户编码', width: 100 },
    { field: '供应商描述', title: '供应商名称', width: 150 },
    { field: '供应商', title: '供应商编码', width: 100 },
    { field: '带符号的本位币金额', title: '金额', width: 120 },
  ]
}

// 显示生成的SQL
function showGeneratedSQL() {
  if (!validateQuery()) {
    return
  }

  const sql = generateSQL()
  if (sql) {
    generatedSQL.value = sql
    showSQLDialog.value = true
  }
}

// 复制SQL到剪贴板
async function copySQLToClipboard() {
  try {
    await navigator.clipboard.writeText(generatedSQL.value)
    ElMessage.success('SQL已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
  }
}

// 处理单元格点击
function handleCellClick(params) {
  console.log('Cell clicked:', params)
}

// 加载示例查询
function loadSampleQuery() {
  // 清空现有面板
  queryPanels.value = []

  // 创建示例查询面板
  const samplePanel = {
    id: generateId(),
    conditions: [
      {
        id: generateId(),
        field: 'date_range',
        operator: 'between',
        value: '',
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        minAmount: null,
        maxAmount: null,
        logic: 'AND'
      },
      {
        id: generateId(),
        field: 'amount_range',
        operator: 'between',
        value: '',
        startDate: '',
        endDate: '',
        minAmount: 1000,
        maxAmount: 100000,
        logic: 'AND'
      },
      {
        id: generateId(),
        field: 'reason',
        operator: 'fuzzy',
        value: '材料,设备',
        startDate: '',
        endDate: '',
        minAmount: null,
        maxAmount: null,
        logic: 'OR'
      }
    ]
  }

  queryPanels.value.push(samplePanel)
  ElMessage.success('已加载示例查询条件')
}

// 组件挂载时初始化
onMounted(() => {
  // 添加初始查询面板
  addQueryPanel()
})
</script>

<style scoped>
.voucher-query-view {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.section-header {
  margin-bottom: 30px;
  text-align: center;
}

.section-header h2 {
  color: #1967d2;
  margin-bottom: 8px;
  font-size: 28px;
  font-weight: 600;
}

.section-header p {
  color: #666;
  font-size: 16px;
}

.query-panels-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.panels-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e8f0fe;
}

.panels-header h3 {
  color: #1967d2;
  font-size: 20px;
  margin: 0;
}

.panel-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.panel-logic {
  margin-left: 16px;
}

.query-panels {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.query-panel {
  border: 2px solid #e8f0fe;
  border-radius: 8px;
  padding: 16px;
  background: #fafbfc;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e0e0e0;
}

.panel-title {
  font-weight: 600;
  color: #1967d2;
  font-size: 16px;
}

.panel-actions {
  display: flex;
  gap: 8px;
}

.conditions-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.condition-row {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.logic-operator {
  min-width: 80px;
}

.field-select {
  min-width: 180px;
}

.operator-select {
  min-width: 120px;
}

.value-input {
  flex: 1;
  min-width: 200px;
}

.condition-actions {
  min-width: 40px;
}

.range-input {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.range-separator {
  color: #666;
  font-weight: 500;
  white-space: nowrap;
}

.text-input {
  width: 100%;
}

.input-hint {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  line-height: 1.2;
}

.sql-preview {
  background: #f5f5f5;
  border-radius: 4px;
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.sql-preview pre {
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 30px 0;
}

.action-buttons .el-button {
  padding: 12px 32px;
  font-size: 16px;
  font-weight: 600;
}

.results-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e8f0fe;
}

.results-header h3 {
  color: #1967d2;
  font-size: 20px;
  margin: 0;
}

.result-count {
  color: #666;
  font-size: 14px;
  background: #e8f0fe;
  padding: 4px 12px;
  border-radius: 16px;
}

.table-container {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e0e0e0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .condition-row {
    flex-wrap: wrap;
    gap: 8px;
  }

  .field-select,
  .operator-select,
  .value-input {
    min-width: 150px;
  }
}

@media (max-width: 768px) {
  .voucher-query-view {
    padding: 12px;
  }

  .panels-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .panel-controls {
    justify-content: space-between;
  }

  .condition-row {
    flex-direction: column;
    align-items: stretch;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
}
</style>
