!function(t,r){var e=t.AscFonts;t.NATIVE_EDITOR_ENJINE&&(t.setImmediate=function(t){t()});var n=void 0!==t?t.fetch:"undefined"!=typeof self?self.fetch:null,a=null;!(t.navigator&&t.navigator.userAgent.toLowerCase().indexOf("ascdesktopeditor")<0)&&(t.location&&"file:"==t.location.protocol||t.document&&t.document.currentScript&&0==t.document.currentScript.src.indexOf("file:///"))?(n=r,a=function(){var t="ascdesktop://fonts/"+U.substr(8);return new Promise((function(r,e){var n=new XMLHttpRequest;n.open("GET",t,!0),n.responseType="arraybuffer",n.overrideMimeType?n.overrideMimeType("text/plain; charset=x-user-defined"):n.setRequestHeader("Accept-Charset","x-user-defined"),n.onload=function(){200==this.status&&r(new Uint8Array(this.response))},n.send(null)}))}):a=function(){return function(){if(!y&&(s||c)&&"function"==typeof n)return n(U,{credentials:"same-origin"}).then((function(t){if(!t.ok)throw"failed to load wasm binary file at '"+U+"'";return t.arrayBuffer()})).catch((function(){return j(U)}));return Promise.resolve().then((function(){return j(U)}))}()},function(){function t(t,r){this.ptr=t,this.length=r}r!==String.prototype.fromUtf8&&r!==String.prototype.toUtf8||(String.prototype.fromUtf8=function(t,e,n){r===e&&(e=0),r===n&&(n=t.length-e);for(var a="",i=e,o=e+n;i<o;){var u=t[i++];if(128&u){var _=63&t[i++];if(192!=(224&u)){var f=63&t[i++];if((u=224==(240&u)?(15&u)<<12|_<<6|f:(7&u)<<18|_<<12|f<<6|63&t[i++])<65536)a+=String.fromCharCode(u);else{var s=u-65536;a+=String.fromCharCode(55296|s>>10,56320|1023&s)}}else a+=String.fromCharCode((31&u)<<6|_)}else a+=String.fromCharCode(u)}return a},String.prototype.toUtf8=function(t){for(var r=this.length,e=new ArrayBuffer(6*r+1),n=0,a=0,i=0,o=new Uint8Array(e);a<r;)(n=this.charCodeAt(a++))>=55296&&n<=57343&&a<r&&(n=65536+((1023&n)<<10|1023&this.charCodeAt(a++))),n<128?o[i++]=n:n<2048?(o[i++]=192|n>>6,o[i++]=128|63&n):n<65536?(o[i++]=224|n>>12,o[i++]=128|n>>6&63,o[i++]=128|63&n):n<2097151?(o[i++]=240|n>>18,o[i++]=128|n>>12&63,o[i++]=128|n>>6&63,o[i++]=128|63&n):n<67108863?(o[i++]=248|n>>24,o[i++]=128|n>>18&63,o[i++]=128|n>>12&63,o[i++]=128|n>>6&63,o[i++]=128|63&n):n<2147483647&&(o[i++]=252|n>>30,o[i++]=128|n>>24&63,o[i++]=128|n>>18&63,o[i++]=128|n>>12&63,o[i++]=128|n>>6&63,o[i++]=128|63&n);return!0!==t&&(o[i++]=0),new Uint8Array(e,0,i)},t.prototype.free=function(){0!==this.ptr&&o._free(this.ptr)},String.prototype.toUtf8Pointer=function(r){var e=this.toUtf8(r),n=o._malloc(e.length);return 0==n?null:(o.HEAP8.set(e,n),new t(n,e.length))})}();var i,o=void 0!==o?o:{},u=Object.assign({},o),_=[],f="./this.program",s=!0,c=!1,p="";(s||c)&&(c?p=self.location.href:"undefined"!=typeof document&&document.currentScript&&(p=document.currentScript.src),p=0!==p.indexOf("blob:")?p.substr(0,p.replace(/[?#].*/,"").lastIndexOf("/")+1):"",t=>{var r=new XMLHttpRequest;return r.open("GET",t,!1),r.send(null),r.responseText},c&&(i=t=>{var r=new XMLHttpRequest;return r.open("GET",t,!1),r.responseType="arraybuffer",r.send(null),new Uint8Array(r.response)}),(t,r,e)=>{var n=new XMLHttpRequest;n.open("GET",t,!0),n.responseType="arraybuffer",n.onload=()=>{200==n.status||0==n.status&&n.response?r(n.response):e()},n.onerror=e,n.send(null)});var l=o.print||console.log.bind(console),h=o.printErr||console.warn.bind(console);Object.assign(o,u),u=null,o.arguments&&(_=o.arguments),o.thisProgram&&(f=o.thisProgram),o.quit&&o.quit;var y,d=0,A=t=>{d=t};o.wasmBinary&&(y=o.wasmBinary);var m;o.noExitRuntime;"object"!=typeof WebAssembly&&I("no native wasm support detected");var g,S,F,v,C=!1,T="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):r;function w(t,r,e){for(var n=r+e,a=r;t[a]&&!(a>=n);)++a;if(a-r>16&&t.buffer&&T)return T.decode(t.subarray(r,a));for(var i="";r<a;){var o=t[r++];if(128&o){var u=63&t[r++];if(192!=(224&o)){var _=63&t[r++];if((o=224==(240&o)?(15&o)<<12|u<<6|_:(7&o)<<18|u<<12|_<<6|63&t[r++])<65536)i+=String.fromCharCode(o);else{var f=o-65536;i+=String.fromCharCode(55296|f>>10,56320|1023&f)}}else i+=String.fromCharCode((31&o)<<6|u)}else i+=String.fromCharCode(o)}return i}function b(t){g=t,o.HEAP8=S=new Int8Array(t),o.HEAP16=new Int16Array(t),o.HEAP32=v=new Int32Array(t),o.HEAPU8=F=new Uint8Array(t),o.HEAPU16=new Uint16Array(t),o.HEAPU32=new Uint32Array(t),o.HEAPF32=new Float32Array(t),o.HEAPF64=new Float64Array(t)}o.INITIAL_MEMORY;var G,H=[],P=[],R=[function(){t.AscFonts.onLoadModule()}];var E=0,x=null,M=null;function I(t){throw o.onAbort&&o.onAbort(t),h(t="Aborted("+t+")"),C=!0,1,t+=". Build with -s ASSERTIONS=1 for more info.",new WebAssembly.RuntimeError(t)}o.preloadedImages={},o.preloadedAudios={};var U,B,L="data:application/octet-stream;base64,";function O(t){return t.startsWith(L)}function j(t){try{if(t==U&&y)return new Uint8Array(y);if(i)return i(t);throw"both async and sync fetching of the wasm failed"}catch(t){I(t)}}function D(t){for(;t.length>0;){var e=t.shift();if("function"!=typeof e){var n=e.func;"number"==typeof n?e.arg===r?k(n)():k(n)(e.arg):n(e.arg===r?null:e.arg)}else e(o)}}O(U="fonts.wasm")||(B=U,U=o.locateFile?o.locateFile(B,p):p+B);var X=[];function k(t){var r=X[t];return r||(t>=X.length&&(X.length=t+1),X[t]=r=G.get(t)),r}function W(t){this.excPtr=t,this.ptr=t-16,this.set_type=function(t){v[this.ptr+4>>2]=t},this.get_type=function(){return v[this.ptr+4>>2]},this.set_destructor=function(t){v[this.ptr+8>>2]=t},this.get_destructor=function(){return v[this.ptr+8>>2]},this.set_refcount=function(t){v[this.ptr>>2]=t},this.set_caught=function(t){t=t?1:0,S[this.ptr+12>>0]=t},this.get_caught=function(){return 0!=S[this.ptr+12>>0]},this.set_rethrown=function(t){t=t?1:0,S[this.ptr+13>>0]=t},this.get_rethrown=function(){return 0!=S[this.ptr+13>>0]},this.init=function(t,r){this.set_type(t),this.set_destructor(r),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var t=v[this.ptr>>2];v[this.ptr>>2]=t+1},this.release_ref=function(){var t=v[this.ptr>>2];return v[this.ptr>>2]=t-1,1===t}}function z(t){this.free=function(){rt(this.ptr),this.ptr=0},this.set_base_ptr=function(t){v[this.ptr>>2]=t},this.get_base_ptr=function(){return v[this.ptr>>2]},this.set_adjusted_ptr=function(t){v[this.ptr+4>>2]=t},this.get_adjusted_ptr_addr=function(){return this.ptr+4},this.get_adjusted_ptr=function(){return v[this.ptr+4>>2]},this.get_exception_ptr=function(){if(ot(this.get_exception_info().get_type()))return v[this.get_base_ptr()>>2];var t=this.get_adjusted_ptr();return 0!==t?t:this.get_base_ptr()},this.get_exception_info=function(){return new W(this.get_base_ptr())},t===r?(this.ptr=tt(8),this.set_adjusted_ptr(0)):this.ptr=t}var q=[];var N=0;function K(t){return rt(new W(t).ptr)}var J={buffers:[null,[],[]],printChar:function(t,r){var e=J.buffers[t];0===r||10===r?((1===t?l:h)(w(e,0)),e.length=0):e.push(r)},varargs:r,get:function(){return J.varargs+=4,v[J.varargs-4>>2]},getStr:function(t){var r=function(t,r){return t?w(F,t,r):""}(t);return r},get64:function(t,r){return t}};function V(t){try{return m.grow(t-g.byteLength+65535>>>16),b(m.buffer),1}catch(t){}}var Y={};function Q(){if(!Q.strings){var t={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:f||"./this.program"};for(var e in Y)Y[e]===r?delete t[e]:t[e]=Y[e];var n=[];for(var e in t)n.push(e+"="+t[e]);Q.strings=n}return Q.strings}var Z,$={r:function(t){return tt(t+16)+16},o:function(t){var r=new z(t),e=r.get_exception_info();return e.get_caught()||(e.set_caught(!0)),e.set_rethrown(!1),q.push(r),function(t){t.add_ref()}(e),r.get_exception_ptr()},s:function(){et(0);var t=q.pop();!function(t){if(t.release_ref()&&!t.get_rethrown()){var r=t.get_destructor();r&&k(r)(t.excPtr),K(t.excPtr)}}(t.get_exception_info()),t.free(),N=0},b:function(){var t=N;if(!t)return A(0),0;var r=new W(t).get_type(),e=new z;if(e.set_base_ptr(t),e.set_adjusted_ptr(t),!r)return A(0),0|e.ptr;for(var n=Array.prototype.slice.call(arguments),a=0;a<n.length;a++){var i=n[a];if(0===i||i===r)break;if(it(i,r,e.get_adjusted_ptr_addr()))return A(i),0|e.ptr}return A(r),0|e.ptr},g:function(){var t=N;if(!t)return A(0),0;var r=new W(t).get_type(),e=new z;if(e.set_base_ptr(t),e.set_adjusted_ptr(t),!r)return A(0),0|e.ptr;for(var n=Array.prototype.slice.call(arguments),a=0;a<n.length;a++){var i=n[a];if(0===i||i===r)break;if(it(i,r,e.get_adjusted_ptr_addr()))return A(i),0|e.ptr}return A(r),0|e.ptr},z:K,q:function(t,r,e){throw new W(t).init(r,e),N=t,t},d:function(t){var r=new z(t),e=r.get_base_ptr();throw N||(N=e),r.free(),e},w:function(t,r,e){return J.varargs=e,0},E:function(t,r,e){return J.varargs=e,0},F:function(t,r,e,n){J.varargs=n},p:function(){I("")},G:function(t,r,e){F.copyWithin(t,r,r+e)},A:function(t){var r,e,n=F.length,a=2147483648;if((t>>>=0)>a)return!1;for(var i=1;i<=4;i*=2){var o=n*(1+.2/i);if(o=Math.min(o,t+100663296),V(Math.min(a,(r=Math.max(t,o))+((e=65536)-r%e)%e)))return!0}return!1},B:function(t,r){var e=0;return Q().forEach((function(n,a){var i=r+e;v[t+4*a>>2]=i,function(t,r,e){for(var n=0;n<t.length;++n)S[r++>>0]=t.charCodeAt(n);e||(S[r>>0]=0)}(n,i),e+=n.length+1})),0},C:function(t,r){var e=Q();v[t>>2]=e.length;var n=0;return e.forEach((function(t){n+=t.length+1})),v[r>>2]=n,0},u:function(t){return 0},D:function(t,r,e,n){var a=J.getStreamFromFD(t),i=J.doReadv(a,r,e);return v[n>>2]=i,0},y:function(t,r,e,n,a){},v:function(t,r,e,n){for(var a=0,i=0;i<e;i++){var o=v[r>>2],u=v[r+4>>2];r+=8;for(var _=0;_<u;_++)J.printChar(t,F[o+_]);a+=u}return v[n>>2]=a,0},a:function(){return d},j:function(t,r){var e=nt();try{return k(t)(r)}catch(t){if(at(e),t!==t+0)throw t;et(1,0)}},e:function(t,r,e){var n=nt();try{return k(t)(r,e)}catch(t){if(at(n),t!==t+0)throw t;et(1,0)}},l:function(t,r,e,n){var a=nt();try{return k(t)(r,e,n)}catch(t){if(at(a),t!==t+0)throw t;et(1,0)}},h:function(t,r,e,n,a){var i=nt();try{return k(t)(r,e,n,a)}catch(t){if(at(i),t!==t+0)throw t;et(1,0)}},I:function(t,r,e,n,a,i){var o=nt();try{return k(t)(r,e,n,a,i)}catch(t){if(at(o),t!==t+0)throw t;et(1,0)}},m:function(t,r,e,n,a,i,o){var u=nt();try{return k(t)(r,e,n,a,i,o)}catch(t){if(at(u),t!==t+0)throw t;et(1,0)}},x:function(t,r,e,n,a,i,o,u,_){var f=nt();try{return k(t)(r,e,n,a,i,o,u,_)}catch(t){if(at(f),t!==t+0)throw t;et(1,0)}},J:function(t){var r=nt();try{k(t)()}catch(t){if(at(r),t!==t+0)throw t;et(1,0)}},c:function(t,r){var e=nt();try{k(t)(r)}catch(t){if(at(e),t!==t+0)throw t;et(1,0)}},i:function(t,r,e){var n=nt();try{k(t)(r,e)}catch(t){if(at(n),t!==t+0)throw t;et(1,0)}},k:function(t,r,e,n){var a=nt();try{k(t)(r,e,n)}catch(t){if(at(a),t!==t+0)throw t;et(1,0)}},n:function(t,r,e,n,a,i,o){var u=nt();try{k(t)(r,e,n,a,i,o)}catch(t){if(at(u),t!==t+0)throw t;et(1,0)}},f:function(t,r,e,n,a){var i=nt();try{k(t)(r,e,n,a)}catch(t){if(at(i),t!==t+0)throw t;et(1,0)}},H:function(t,r,e,n,a,i,o){var u=nt();try{k(t)(r,e,n,a,i,o)}catch(t){if(at(u),t!==t+0)throw t;et(1,0)}},t:function(t){return t}},tt=(function(){var t={a:$};function r(t,r){var e,n=t.exports;o.asm=n,m=o.asm?.K,b(m.buffer),G=o.asm?.M,e=o.asm?.L,P.unshift(e),function(t){if(E--,o.monitorRunDependencies&&o.monitorRunDependencies(E),0==E&&(null!==x&&(clearInterval(x),x=null),M)){var r=M;M=null,r()}}()}function e(t){r(t.instance)}function i(r){return a().then((function(r){return WebAssembly.instantiate(r,t)})).then((function(t){return t})).then(r,(function(t){h("failed to asynchronously prepare wasm: "+t),I(t)}))}if(E++,o.monitorRunDependencies&&o.monitorRunDependencies(E),o.instantiateWasm)try{return o.instantiateWasm(t,r)}catch(t){return h("Module.instantiateWasm callback failed with error: "+t),!1}y||"function"!=typeof WebAssembly.instantiateStreaming||O(U)||"function"!=typeof n?i(e):n(U,{credentials:"same-origin"}).then((function(r){return WebAssembly.instantiateStreaming(r,t).then(e,(function(t){return h("wasm streaming compile failed: "+t),h("falling back to ArrayBuffer instantiation"),i(e)}))}))}(),o.___wasm_call_ctors=function(){return(o.___wasm_call_ctors=o.asm?.L)?.apply(null,arguments)},o._malloc=function(){return(tt=o._malloc=o.asm?.N)?.apply(null,arguments)}),rt=o._free=function(){return(rt=o._free=o.asm?.O)?.apply(null,arguments)},et=(o._ASC_FT_Malloc=function(){return(o._ASC_FT_Malloc=o.asm?.P)?.apply(null,arguments)},o._ASC_FT_Free=function(){return(o._ASC_FT_Free=o.asm?.Q)?.apply(null,arguments)},o._ASC_FT_Init=function(){return(o._ASC_FT_Init=o.asm?.R)?.apply(null,arguments)},o._ASC_FT_Done_FreeType=function(){return(o._ASC_FT_Done_FreeType=o.asm?.S)?.apply(null,arguments)},o._ASC_FT_Set_TrueType_HintProp=function(){return(o._ASC_FT_Set_TrueType_HintProp=o.asm?.T)?.apply(null,arguments)},o._ASC_FT_Open_Face=function(){return(o._ASC_FT_Open_Face=o.asm?.U)?.apply(null,arguments)},o._ASC_FT_Done_Face=function(){return(o._ASC_FT_Done_Face=o.asm?.V)?.apply(null,arguments)},o._ASC_FT_SetCMapForCharCode=function(){return(o._ASC_FT_SetCMapForCharCode=o.asm?.W)?.apply(null,arguments)},o._ASC_FT_GetFaceInfo=function(){return(o._ASC_FT_GetFaceInfo=o.asm?.X)?.apply(null,arguments)},o._ASC_FT_GetFaceMaxAdvanceX=function(){return(o._ASC_FT_GetFaceMaxAdvanceX=o.asm?.Y)?.apply(null,arguments)},o._ASC_FT_GetKerningX=function(){return(o._ASC_FT_GetKerningX=o.asm?.Z)?.apply(null,arguments)},o._ASC_FT_Set_Transform=function(){return(o._ASC_FT_Set_Transform=o.asm?._)?.apply(null,arguments)},o._ASC_FT_Set_Char_Size=function(){return(o._ASC_FT_Set_Char_Size=o.asm?.$)?.apply(null,arguments)},o._ASC_FT_Load_Glyph=function(){return(o._ASC_FT_Load_Glyph=o.asm?.aa)?.apply(null,arguments)},o._ASC_FT_Glyph_Get_CBox=function(){return(o._ASC_FT_Glyph_Get_CBox=o.asm?.ba)?.apply(null,arguments)},o._ASC_FT_Get_Glyph_Measure_Params=function(){return(o._ASC_FT_Get_Glyph_Measure_Params=o.asm?.ca)?.apply(null,arguments)},o._ASC_FT_Get_Glyph_Render_Params=function(){return(o._ASC_FT_Get_Glyph_Render_Params=o.asm?.da)?.apply(null,arguments)},o._ASC_FT_Get_Glyph_Render_Buffer=function(){return(o._ASC_FT_Get_Glyph_Render_Buffer=o.asm?.ea)?.apply(null,arguments)},o._ASC_HB_LanguageFromString=function(){return(o._ASC_HB_LanguageFromString=o.asm?.fa)?.apply(null,arguments)},o._ASC_HB_ShapeText=function(){return(o._ASC_HB_ShapeText=o.asm?.ga)?.apply(null,arguments)},o._ASC_HB_FontFree=function(){return(o._ASC_HB_FontFree=o.asm?.ha)?.apply(null,arguments)},o._setThrew=function(){return(et=o._setThrew=o.asm?.ia)?.apply(null,arguments)}),nt=o.stackSave=function(){return(nt=o.stackSave=o.asm?.ja)?.apply(null,arguments)},at=o.stackRestore=function(){return(at=o.stackRestore=o.asm?.ka)?.apply(null,arguments)},it=o.___cxa_can_catch=function(){return(it=o.___cxa_can_catch=o.asm?.la)?.apply(null,arguments)},ot=o.___cxa_is_pointer_type=function(){return(ot=o.___cxa_is_pointer_type=o.asm?.ma)?.apply(null,arguments)};function ut(t){function r(){Z||(Z=!0,o.calledRun=!0,C||(!0,D(P),o.onRuntimeInitialized&&o.onRuntimeInitialized(),function(){if(o.postRun)for("function"==typeof o.postRun&&(o.postRun=[o.postRun]);o.postRun.length;)t=o.postRun.shift(),R.unshift(t);var t;D(R)}()))}t=t||_,E>0||(!function(){if(o.preRun)for("function"==typeof o.preRun&&(o.preRun=[o.preRun]);o.preRun.length;)t=o.preRun.shift(),H.unshift(t);var t;D(H)}(),E>0||(o.setStatus?(o.setStatus("Running..."),setTimeout((function(){setTimeout((function(){o.setStatus("")}),1),r()}),1)):r()))}if(M=function t(){Z||ut(),Z||(M=t)},o.run=ut,o.preInit)for("function"==typeof o.preInit&&(o.preInit=[o.preInit]);o.preInit.length>0;)o.preInit.pop()();function _t(){this.error=0,this.freeObj=0}ut(),_t.prototype.free=function(){o._ASC_FT_Free(this.freeObj)};let ft=new _t,st=new _t;function ct(t){this.size=t,this.pointer=o._malloc(t)}st.count=0,e.CopyStreamToMemory=function(t,r){var e=o._ASC_FT_Malloc(r);return o.HEAP8?.set(t,e),e},e.isAsmLoaded=function(){return!!o.asm},ct.prototype.getBuffer=function(){return new Uint8Array(o.HEAPU8?.buffer,this.pointer,this.size)},ct.prototype.free=function(){o._free(this.pointer)},ct.prototype.set=function(t,r){o.HEAPU8&&(o.HEAPU8[this.pointer+t]=r)},e.AllocString=function(t){return new ct(t)},e.FT_CreateLibrary=o._ASC_FT_Init,e.FT_Done_Library=o._ASC_FT_Done_FreeType,e.FT_Set_TrueType_HintProp=o._ASC_FT_Set_TrueType_HintProp,e.FT_Open_Face=o._ASC_FT_Open_Face,e.FT_Done_Face=o._ASC_FT_Done_Face,e.FT_SetCMapForCharCode=o._ASC_FT_SetCMapForCharCode,e.FT_GetKerningX=o._ASC_FT_GetKerningX,e.FT_GetFaceMaxAdvanceX=o._ASC_FT_GetFaceMaxAdvanceX,e.FT_Set_Transform=o._ASC_FT_Set_Transform,e.FT_Set_Char_Size=o._ASC_FT_Set_Char_Size,e.FT_GetFaceInfo=function(t,r){if(!o.asm?.X)return ft.error=1,ft;let e=o._ASC_FT_GetFaceInfo?.(t);if(!e)return ft.error=1,ft;var n=Math.min(o.HEAP8.length-e,1e3);return r.init(new Uint8Array(o.HEAP8.buffer,e,n)),ft.freeObj=e,ft.error=0,ft},e.FT_Load_Glyph=o._ASC_FT_Load_Glyph,e.FT_SetCMapForCharCode=o._ASC_FT_SetCMapForCharCode,e.FT_Get_Glyph_Measure_Params=function(t,r,e){let n=o._ASC_FT_Get_Glyph_Measure_Params?.(t,r?1:0);if(!n)return st.error=1,st;let a=r?o.HEAP32[n>>2]:15;return r&&(a=o.HEAP32[n>>2]),e.init(new Uint8Array(o.HEAP8.buffer,n+4,4*(a-1))),st.freeObj=n,st.count=a,st.error=0,st},e.FT_Get_Glyph_Render_Params=function(t,r,e){let n=o._ASC_FT_Get_Glyph_Render_Params?.(t,r);return n?(e.init(new Uint8Array(o.HEAP8.buffer,n,24)),ft.freeObj=n,ft.error=0,ft):(st.error=1,st)},e.FT_Get_Glyph_Render_Buffer=function(t,r){var e=o._ASC_FT_Get_Glyph_Render_Buffer(t);return new Uint8Array(o.HEAP8.buffer,e,r)};let pt={};e.HB_FontFree=o.ASC_HB_FontFree,e.HB_ShapeText=function(t,r,e,n,a,i,u){if(!pt[i]){let t=i.toUtf8();var _=o._malloc(t.length);o.HEAP8.set(t,t),pt[i]=o._ASC_HB_LanguageFromString(_),o._free(_)}let f=o._ASC_HB_ShapeText?.(t.GetFace(),t.GetHBFont(),r.pointer,e,n,a,pt[i]);if(!f)return st.error=1,st;let s=o.HEAP8,c=(255&s[f+3])<<24|(255&s[f+2])<<16|(255&s[f+1])<<8|255&s[f];return u.init(s,f+4,c-4),t.SetHBFont(u.readPointer64()),st.freeObj=f,st.count=(c-12)/26,st.error=0,st},e.onLoadModule()}(window,void 0);