<template>
  <div class="view-container">
    <div class="section-header">
      <h2>财务报表结账预计系统</h2>
      <p>结账前数据校验器</p>
    </div>

    <div class="action-buttons">
      <!-- 主要操作按钮组 -->
      <div class="button-group primary-actions">
        <button @click="fetchData" class="action-button fetch">取数</button>
        <button @click="fetchDataDifference" class="action-button fetch">获取缺失差异</button>
        <button @click="intelfillCloseTamplate" class="action-button fetch">填充结账模板</button>
        <button @click="handleFillRedTextToIncomeSheet" class="action-button fetch">利润中心补全</button>
      </div>

      <!-- 保存操作按钮组 -->
      <div class="button-group save-actions">
        <button @click="saveData" class="action-button save">推送结账后台</button>
        <button @click="saveData2" class="action-button save">保存</button>
        <button @click="openRemarkDialog" class="action-button export">保存至新版本</button>
      </div>

      <!-- 版本管理和导入导出按钮组 -->
      <div class="button-group import-export-actions">
        <div class="period-input-group">
          <div class="period-input-container">
            <input v-model="selectedPeriod" type="text" class="period-input" readonly placeholder="请选择版本..."
              @click="toggleDropdown" />
            <button @click.stop="toggleDropdown" class="action-button dropdown-toggle"
              :class="{ active: showDropdown }">
              <span class="dropdown-icon">{{ showDropdown ? '▲' : '▼' }}</span>
            </button>
          </div>
          <transition name="fade">
            <div v-if="showDropdown" class="dropdown-content" @click.stop>
              <div v-if="loadingPeriods" class="loading-text">加载中...</div>
              <div v-else-if="periods.length === 0" class="no-data">没有可用的数据</div>
              <template v-else>
                <button v-for="(period, idx) in periods" :key="idx" class="dropdown-item">
                  <span @click="selectPeriod(period[0], `${period[0]} - ${period[1]} - ${period[2]}`)"
                    class="period-label">
                    {{ period[0] }} - {{ period[1] }} - {{ period[2] }}
                  </span>
                  <button @click.stop="deletePeriod(period[0])" class="delete-button" title="删除此版本">删除</button>
                </button>
              </template>
            </div>
          </transition>
        </div>

        <button @click="triggerFileInput" class="action-button import">
          <span class="button-icon">📁</span>
          导入Excel
        </button>
        <button @click="exportAllSheets" class="action-button export">
          <span class="button-icon">📊</span>
          导出Excel
        </button>
      </div>

      <input ref="fileInput" type="file" accept=".xlsx,.xls" style="display:none" @change="importExcel" />
    </div>
    <div ref="container" class="table-container"></div>
    <!-- 备注输入弹窗 -->
    <div v-if="showRemarkDialog" class="remark-dialog-mask">
      <div class="remark-dialog">
        <div class="remark-title">请输入备注</div>
        <textarea v-model="remarkText" rows="3" class="remark-input" placeholder="请输入保存备注"></textarea>
        <div class="remark-actions">
          <button @click="confirmRemark" class="action-button save">确定</button>
          <button @click="showRemarkDialog = false" class="action-button export">取消</button>
        </div>
      </div>
    </div>

    <!-- 数据源选择弹窗 -->
    <div v-if="showDataSourceDialog" class="data-source-dialog-mask">
      <div class="data-source-dialog">
        <div class="dialog-header">
          <h3 class="dialog-title">选择数据源</h3>
          <button @click="showDataSourceDialog = false" class="close-button">×</button>
        </div>

        <div class="dialog-content">
          <div class="data-source-options">
            <div class="option-group">
              <label class="radio-option" :class="{ active: selectedDataSource === 'balance' }">
                <input type="radio" v-model="selectedDataSource" value="balance" class="radio-input" />
                <div class="radio-content">
                  <div class="radio-icon">📊</div>
                  <div class="radio-text">
                    <div class="radio-title">科目余额表取数</div>
                    <div class="radio-desc">从科目余额表获取数据</div>
                  </div>
                </div>
              </label>

              <label class="radio-option" :class="{ active: selectedDataSource === 'detail' }">
                <input type="radio" v-model="selectedDataSource" value="detail" class="radio-input" />
                <div class="radio-content">
                  <div class="radio-icon">📋</div>
                  <div class="radio-text">
                    <div class="radio-title">明细帐取数</div>
                    <div class="radio-desc">从明细帐获取数据，需选择年份月份</div>
                  </div>
                </div>
              </label>
            </div>

            <!-- 明细帐选项的年份月份选择 -->
            <transition name="slide-down">
              <div v-if="selectedDataSource === 'detail'" class="date-selection">
                <div class="date-inputs">
                  <div class="input-group">
                    <label class="input-label">年份</label>
                    <select v-model="selectedYear" class="date-select">
                      <option v-for="year in yearOptions" :key="year" :value="year">
                        {{ year }}年
                      </option>
                    </select>
                  </div>

                  <div class="input-group">
                    <label class="input-label">月份</label>
                    <select v-model="selectedMonth" class="date-select">
                      <option v-for="month in monthOptions" :key="month" :value="month">
                        {{ month }}月
                      </option>
                    </select>
                  </div>
                </div>
              </div>
            </transition>
          </div>
        </div>

        <div class="dialog-footer">
          <button @click="showDataSourceDialog = false" class="dialog-button cancel">取消</button>
          <button @click="confirmDataSource" class="dialog-button confirm" :disabled="!selectedDataSource">确定</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, onBeforeUnmount, ref } from 'vue'

import { createUniver, defaultTheme, FUniver, LocaleType, merge, Univer } from '@univerjs/presets';
import { UniverSheetsCorePreset } from '@univerjs/presets/preset-sheets-core';
import UniverPresetSheetsCoreZhCN from '@univerjs/presets/preset-sheets-core/locales/zh-CN';
import '@univerjs/presets/lib/styles/preset-sheets-core.css';


import { UniverSheetsConditionalFormattingPreset } from '@univerjs/presets/preset-sheets-conditional-formatting'
import sheetsConditionalFormattingZhCN from '@univerjs/presets/preset-sheets-conditional-formatting/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-conditional-formatting.css'

import { UniverSheetsDataValidationPreset } from '@univerjs/presets/preset-sheets-data-validation'
import sheetsDataValidationZhCN from '@univerjs/presets/preset-sheets-data-validation/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-data-validation.css'

import { UniverSheetsDrawingPreset } from '@univerjs/presets/preset-sheets-drawing'
import sheetsDrawingZhCN from '@univerjs/presets/preset-sheets-drawing/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-drawing.css'

import { UniverSheetsFilterPreset } from '@univerjs/presets/preset-sheets-filter'
import sheetsFilterZhCN from '@univerjs/presets/preset-sheets-filter/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-filter.css'

import { UniverSheetsHyperLinkPreset } from '@univerjs/presets/preset-sheets-hyper-link'
import sheetsHyperLinkZhCN from '@univerjs/presets/preset-sheets-hyper-link/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-hyper-link.css'

import fillDifference from '../scripts/fillDifference'
import fetchSnapShot from '../scripts/snapShot'
import fillCloseTamplate, { fillCloseTamplate2 } from '../scripts/fillCloseTamplate'
import { fillRedTextToIncomeSheet } from '../scripts/fillCloseTamplate';
import * as ExcelJS from 'exceljs';

const container = ref<HTMLElement | null>(null)
const fileInput = ref<HTMLInputElement | null>(null)
let univerInstance: Univer | null = null
let univerAPIInstance: FUniver | null = null

const showDropdown = ref(false);
const periods = ref<Array<{ id: string, name: string }>>([]);
const loadingPeriods = ref(false);
const selectedPeriod = ref('');
const selectedPeriodId = ref('');

const toggleDropdown = async () => {
  console.log('Toggle dropdown called, current state:', showDropdown.value);
  showDropdown.value = !showDropdown.value;
  if (showDropdown.value && periods.value.length === 0) {
    console.log('Fetching available periods...');
    await fetchAvailablePeriods();
  } else {
    console.log('Using cached periods:', periods.value);
  }
};

const fetchAvailablePeriods = async () => {
  try {
    loadingPeriods.value = true;
    console.log('Starting to fetch periods from API...');
    const response = await fetch('http://localhost:8000/api/get-available-periods', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({})
    });

    console.log('API response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response:', errorText);
      throw new Error(`获取期数列表失败: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('Received periods data:', data);
    periods.value = data;
  } catch (error) {
    console.error('获取期数列表时出错:', error);
    // 显示错误提示
    alert('获取期数列表失败: ' + (error.message || '未知错误'));
  } finally {
    loadingPeriods.value = false;
    console.log('Finished loading periods, loading state:', loadingPeriods.value);
  }
};

const selectPeriod = async (periodId: string, periodLabel: string) => {
  try {
    selectedPeriod.value = periodLabel;
    selectedPeriodId.value = periodId;
    showDropdown.value = false;
    const response = await fetch('http://localhost:8000/api/get-snapsheet', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ periodId })
    });

    if (!response.ok) {
      throw new Error(`获取快照失败: ${response.status} ${response.statusText}`);
    }

    const snapsheet = await response.json();
    const unitId = univerAPIInstance?.getActiveWorkbook()?.getId();
    if (unitId) {
      univerAPIInstance?.disposeUnit(unitId);
    }
    univerAPIInstance?.createWorkbook(snapsheet);
  } catch (error) {
    console.error('导入数据时出错:', error);
    alert('导入数据失败: ' + (error.message || '未知错误'));
  }
};

const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  if (!target.closest('.dropdown')) {
    showDropdown.value = false;
  }
};

// Function to automatically select the latest period
const selectLatestPeriod = async () => {
  try {
    await fetchAvailablePeriods();
    if (periods.value.length > 0) {
      // Assuming periods are sorted, get the last one (latest)
      const latestPeriod = periods.value[periods.value.length - 1];
      const periodLabel = `${latestPeriod[0]} - ${latestPeriod[1]} - ${latestPeriod[2]}`;
      await selectPeriod(latestPeriod[0], periodLabel);
    }
  } catch (error) {
    console.error('Failed to select latest period:', error);
  }
};

onMounted(async () => {
  // First initialize the Univer instance
  const { univer, univerAPI } = createUniver({
    locale: LocaleType.ZH_CN,
    locales: {
      [LocaleType.ZH_CN]: merge(
        {},
        UniverPresetSheetsCoreZhCN,
        sheetsConditionalFormattingZhCN,
        sheetsDataValidationZhCN,
        sheetsDrawingZhCN,
        sheetsFilterZhCN,
        sheetsHyperLinkZhCN,
      ),
    },
    theme: defaultTheme,
    presets: [
      UniverSheetsCorePreset({
        container: container.value as HTMLElement,
      }),
      UniverSheetsConditionalFormattingPreset(),
      UniverSheetsDataValidationPreset(),
      UniverSheetsDrawingPreset(),
      UniverSheetsFilterPreset(),
      UniverSheetsHyperLinkPreset(),
    ],
  })
  //const fWorkbook1 = univerAPI.createWorkbook({id:"Sheet1",name:"财务报表"})
  //const sheet1=fWorkbook1.create("收入成本测算",100,10)
  //const sheet2=fWorkbook1.create("快速取数",100,10)
  //fWorkbook1.deleteSheet(fWorkbook1.getSheetByName("Sheet1"))
  //sheet1.activate()
  univerInstance = univer;
  univerAPIInstance = univerAPI;
  fetchSnapShot(univerAPI);
  document.addEventListener('click', handleClickOutside);

  // After initialization, try to select the latest period
  await selectLatestPeriod();
  // Automatically fetch and select the latest period when component mounts
  selectLatestPeriod();
})

onBeforeUnmount(() => {
  univerInstance?.dispose()
  univerAPIInstance?.dispose()
  univerInstance = null
  univerAPIInstance = null
  document.removeEventListener('click', handleClickOutside);
})

// 数据源选择弹窗相关状态
const showDataSourceDialog = ref(false)
const selectedDataSource = ref('')
const selectedYear = ref(new Date().getFullYear())
const selectedMonth = ref(new Date().getMonth() + 1)

// 年份和月份选项
const yearOptions = (() => {
  const currentYear = new Date().getFullYear()
  const years = []
  for (let i = currentYear - 5; i <= currentYear + 1; i++) {
    years.push(i)
  }
  return years
})()

const monthOptions = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]

// 打开数据源选择弹窗
function openDataSourceDialog() {
  showDataSourceDialog.value = true
  selectedDataSource.value = ''
  selectedYear.value = new Date().getFullYear()
  selectedMonth.value = new Date().getMonth() + 1
}

// 确认数据源选择
async function confirmDataSource() {
  if (!selectedDataSource.value) {
    alert('请选择数据源类型')
    return
  }

  showDataSourceDialog.value = false
  await fetchDataWithParams()
}

async function fetchData() {
  openDataSourceDialog()
}

async function fetchDataWithParams() {
  try {
    const requestBody = {
      dataSource: selectedDataSource.value,
      year: selectedDataSource.value === 'detail' ? selectedYear.value : undefined,
      month: selectedDataSource.value === 'detail' ? selectedMonth.value : undefined
    }

    const response = await fetch('http://localhost:8000/api/budget-data', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });
    const data = await response.json();

    if (!Array.isArray(data)) {
      throw new Error('返回数据格式错误');
    }
    const sheet0 = univerAPIInstance?.getActiveWorkbook()?.getSheetByName('收入成本测算');
    const arr = sheet0.getRange(0, 0, sheet0.getLastRow() + 1, sheet0.getLastColumn() + 1).getValues();
    var localIndex = 0;
    for (let i = 0; i < arr[0].length; i++) {
      if (arr[0][i] == '定位符') { localIndex = i }
    };
    var localMap = new Map();
    for (let i = 1; i < arr.length; i++) {
      localMap.set(arr[i][localIndex], i)
    };

    const sheet = univerAPIInstance?.getActiveWorkbook()?.getSheetByName('快速取数');
    if (sheet) {
      sheet.deleteRows(1, sheet.getLastRow())
      sheet.setRowCount(data[0].length)
      sheet.setColumnCount(data[0][0].length)
      sheet.getRange(0, 0, data[0].length, data[0][0].length).setValues(data[0])
    }

    for (let i = 1; i < data[0].length; i++) {
      if (!localMap.has(data[0][i][0])) {
        sheet.getRange(i, 0).setFontColor('red')
      }
    }

    const sheet2 = univerAPIInstance?.getActiveWorkbook()?.getSheetByName('快速科目余额表');
    if (sheet2) {
      sheet2.deleteRows(1, sheet2.getLastRow())
      sheet2.setRowCount(data[1].length)
      sheet2.setColumnCount(data[1][0].length)
      sheet2.getRange(0, 0, data[1].length, data[1][0].length).setValues(data[1])
    }
  } catch (error) {
    alert('获取数据失败: ' + error.message);
  }
}


const saveData = async () => {
  try {
    const sheet = univerAPIInstance?.getActiveWorkbook()?.getSheetByName('收入成本测算');
    if (!sheet) {
      throw new Error('无法获取收入成本测算表');
    }
    const range = sheet.getRange(0, 0, sheet.getLastRow() + 1, sheet.getLastColumn() + 1);
    const array1 = range.getValues();

    const sheet2 = univerAPIInstance?.getActiveWorkbook()?.getSheetByName('独立结账模板收入成本');
    if (!sheet2) {
      throw new Error('独立结账模板收入成本');
    }
    const range2 = sheet2.getRange(0, 0, sheet2.getLastRow() + 1, sheet2.getLastColumn() + 1);
    const array2 = range2.getValues();

    const sheet3 = univerAPIInstance?.getActiveWorkbook()?.getSheetByName('独立结账模板安全费');
    if (!sheet3) {
      throw new Error('独立结账模板安全费');
    }
    const range3 = sheet3.getRange(0, 0, sheet3.getLastRow() + 1, sheet3.getLastColumn() + 1);
    const array3 = range3.getValues();

    const sheet4 = univerAPIInstance?.getActiveWorkbook()?.getSheetByName('批量暂估');
    if (!sheet4) {
      throw new Error('批量暂估');
    }
    const range4 = sheet4.getRange(0, 0, sheet4.getLastRow() + 1, sheet4.getLastColumn() + 1);
    const array4 = range4.getValues();

    const data = { "收入成本测算": array1, "独立结账模板收入成本": array2, "独立结账模板安全费": array3, "批量暂估": array4 }
    const response = await fetch('http://localhost:8000/api/save-budget', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data),
      // 确保请求不会被缓存
      cache: 'no-cache'
    });

    if (!response.ok) {
      throw new Error(`保存失败: ${response.status} ${response.statusText}`);
    }

    alert('数据已成功保存！');
  } catch (error) {
    console.error('保存数据出错:', error);
    alert('保存数据失败: ' + error.message);
  }
}

const showRemarkDialog = ref(false)
const remarkText = ref('')

function openRemarkDialog() {
  remarkText.value = ''
  showRemarkDialog.value = true
}

async function intelfillCloseTamplate() {
  try {
    console.log('开始填充结账模板...');

    // 填充安全费模板
    await fillCloseTamplate(univerAPIInstance);
    console.log('安全费模板填充完成');

    // 填充收入成本模板
    await fillCloseTamplate2(univerAPIInstance);
    console.log('收入成本模板填充完成');

    alert('结账模板填充完成！');
  } catch (error) {
    console.error('填充结账模板时发生错误:', error);
    alert('填充结账模板失败: ' + (error.message || '未知错误'));
  }
}

async function fetchDataDifference() {
  await fillDifference(univerAPIInstance);
}

async function handleFillRedTextToIncomeSheet() {
  try {
    console.log('开始利润中心补全...');
    await fillRedTextToIncomeSheet(univerAPIInstance);
    console.log('利润中心补全完成');
    alert('利润中心补全完成！');
  } catch (error) {
    console.error('利润中心补全时发生错误:', error);
    alert('利润中心补全失败: ' + (error.message || '未知错误'));
  }
}

async function confirmRemark() {
  showRemarkDialog.value = false
  await exportData(remarkText.value)
}

const exportData = async (remark = '') => {
  const snapsheet = univerAPIInstance?.getActiveWorkbook()?.save();
  try {
    console.log('开始保存快照...');
    const response = await fetch('http://localhost:8000/api/save-snapsheet', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify([
        snapsheet,
        remark] // 备注字段
      ),
      cache: 'no-cache'
    });

    console.log('请求发送完成，状态:', response.status, response.ok ? '成功' : '失败');

    if (!response.ok) {
      throw new Error(`保存快照失败: ${response.status} ${response.statusText}`);
    }

    alert('快照已成功保存！');
  } catch (error) {
    console.error('保存快照出错:', error);
    alert('保存快照失败: ' + error.message);
  }
}

const saveData2 = async () => {
  const snapsheet = univerAPIInstance?.getActiveWorkbook()?.save();
  try {
    console.log('开始保存快照...');
    const response = await fetch('http://localhost:8000/api/save-snapsheet-new', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify([
        selectedPeriodId.value,
        snapsheet
      ]),
      cache: 'no-cache'
    });

    console.log('请求发送完成，状态:', response.status, response.ok ? '成功' : '失败');

    if (!response.ok) {
      throw new Error(`保存快照失败: ${response.status} ${response.statusText}`);
    }

    alert('快照已成功保存！');
  } catch (error) {
    console.error('保存快照出错:', error);
    alert('保存快照失败: ' + error.message);
  }
}





// 触发隐藏文件选择框
function triggerFileInput() {
  fileInput.value?.click();
}

// 导入excel并写入universheet
async function importExcel(event: Event) {
  const input = event.target as HTMLInputElement;
  if (!input.files || input.files.length === 0) return;
  const file = input.files[0];
  const reader = new FileReader();
  reader.onload = async (e) => {
    const buffer = e.target?.result;
    if (!buffer) return;
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(buffer as ArrayBuffer);
    const univerWorkbook = univerAPIInstance?.getActiveWorkbook();
    if (!univerWorkbook) return;
    for (const worksheet of workbook.worksheets) {
      // 检查是否存在同名sheet，不存在则创建
      let univerSheet = univerWorkbook.getSheetByName(worksheet.name);
      if (!univerSheet) {
        univerSheet = univerWorkbook.create(worksheet.name, worksheet.rowCount, worksheet.columnCount);
      }
      // 读取exceljs sheet数据，支持公式
      const rows = [];
      worksheet.eachRow({ includeEmpty: true }, (row) => {
        const rowData = [];
        // 获取行中所有单元格（包括空值）
        for (let col = 1; col <= worksheet.columnCount; col++) {
          const cell = row.getCell(col);
          if (cell.formula) {
            // 处理公式，移除_xlfn.前缀并添加等号
            const formula = cell.formula.replace(/_xlfn\./g, '');
            rowData.push('=' + formula);
          } else {
            // 否则使用原始值，如果为undefined则设为null
            rowData.push(cell.value !== undefined ? cell.value : null);
          }
        }
        rows.push(rowData);
      });
      // 写入universheet
      if (univerSheet) {
        const range = univerSheet.getRange(0, 0, rows.length, worksheet.columnCount);
        console.log(rows)
        range.setValues(rows);
      }
    }
    input.value = '';
  };
  reader.readAsArrayBuffer(file);
}

const deletePeriod = async (periodId: string) => {
  if (!window.confirm('确定要删除该版本吗？此操作不可恢复！')) return;
  try {
    const response = await fetch('http://localhost:8000/api/delete-snapsheet', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(periodId)
    });
    if (!response.ok) {
      throw new Error(`删除失败: ${response.status} ${response.statusText}`);
    }
    alert('删除成功！');
    await fetchAvailablePeriods(); // 刷新列表
  } catch (error: any) {
    alert('删除失败: ' + (error.message || '未知错误'));
  }
}

// 导出所有表格到Excel
const exportAllSheets = async () => {
  try {
    const workbook = new ExcelJS.Workbook();
    const univerWorkbook = univerAPIInstance?.getActiveWorkbook();

    if (!univerWorkbook) {
      alert('没有可导出的工作簿');
      return;
    }

    // 获取所有工作表
    const sheets = univerWorkbook.getSheets();

    if (sheets.length === 0) {
      alert('没有可导出的工作表');
      return;
    }

    // 遍历每个工作表并添加到Excel工作簿
    for (const sheet of sheets) {
      // 获取工作表名称 - 使用 getSheet() 方法获取底层sheet对象
      const sheetSnapshot = sheet.getSheet().getSnapshot();
      const sheetName = sheetSnapshot.name || 'Sheet';
      const worksheet = workbook.addWorksheet(sheetName);

      // 获取工作表数据
      const lastRow = sheet.getLastRow();
      const lastCol = sheet.getLastColumn();

      if (lastRow >= 0 && lastCol >= 0) {
        const range = sheet.getRange(0, 0, lastRow + 1, lastCol + 1);
        const values = range.getValues();
        const formulas = range.getFormulas();

        // 设置数据和公式
        for (let row = 0; row < values.length; row++) {
          for (let col = 0; col < values[row].length; col++) {
            const cell = worksheet.getCell(row + 1, col + 1);

            // 检查是否有公式
            if (formulas && formulas[row] && formulas[row][col]) {
              // 设置公式，确保以等号开头
              const formula = formulas[row][col];
              cell.value = { formula: formula.startsWith('=') ? formula.substring(1) : formula };
            } else {
              // 设置值 - 处理可能的null值
              const cellValue = values[row][col];
              if (cellValue !== null && cellValue !== undefined) {
                cell.value = cellValue as any;
              } else {
                cell.value = '';
              }
            }
          }
        }

        // 设置基本样式
        if (values.length > 0) {
          // 设置表头样式（第一行）
          const headerRow = worksheet.getRow(1);
          headerRow.eachCell((cell) => {
            cell.font = { bold: true, color: { argb: 'FFFFFF' } };
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: '4472C4' }
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' }
            };
          });

          // 设置数据行样式
          for (let row = 2; row <= values.length; row++) {
            const dataRow = worksheet.getRow(row);
            dataRow.eachCell((cell) => {
              cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' }
              };
              cell.alignment = { horizontal: 'left', vertical: 'middle' };
            });
          }

          // 自动调整列宽
          worksheet.columns.forEach((column, index) => {
            let maxLength = 0;
            for (let row = 1; row <= values.length; row++) {
              const cell = worksheet.getCell(row, index + 1);
              const cellValue = cell.value ? cell.value.toString() : '';
              if (cellValue.length > maxLength) {
                maxLength = cellValue.length;
              }
            }
            column.width = Math.min(Math.max(maxLength + 2, 10), 50);
          });
        }
      }
    }

    // 生成文件并下载
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });

    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    link.setAttribute('href', url);
    link.setAttribute('download', `财务报表_${timestamp}.xlsx`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    alert('导出成功！');
  } catch (error) {
    console.error('导出失败:', error);
    alert('导出失败: ' + (error.message || '未知错误'));
  }
}

</script>

<style scoped>
.view-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 24px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 16px;
  align-items: flex-start;
}

.button-group {
  display: flex;
  gap: 6px;
  align-items: center;
  padding: 8px 12px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.button-group.primary-actions {
  background-color: #e6f7ff;
  border-color: #91d5ff;
}

.button-group.save-actions {
  background-color: #f6ffed;
  border-color: #b7eb8f;
}

.button-group.import-export-actions {
  background-color: #fff7e6;
  border-color: #ffd591;
  flex: 1;
  min-width: 400px;
}

.action-button {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  font-size: 13px;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
}

.button-icon {
  font-size: 14px;
}

.action-button.fetch {
  background-color: #1890ff;
  color: white;
}

.action-button.fetch:hover {
  background-color: #40a9ff;
}

.action-button.save {
  background-color: #52c41a;
  color: white;
}

.action-button.save:hover {
  background-color: #73d13d;
}

.action-button.export {
  background-color: #fa8c16;
  color: white;
}

.action-button.export:hover {
  background-color: #ffa940;
}

.action-button.import {
  background-color: #722ed1;
  color: white;
}

.action-button.import:hover {
  background-color: #9254de;
}

.action-button.dropdown-toggle {
  background-color: #f0f0f0;
  color: #666;
  padding: 6px 8px;
  min-width: 32px;
  justify-content: center;
}

.action-button.dropdown-toggle:hover {
  background-color: #e6e6e6;
  color: #333;
}

.action-button.dropdown-toggle.active {
  background-color: #1890ff;
  color: white;
}

.dropdown-icon {
  font-size: 12px;
  transition: transform 0.3s;
}

.table-container {
  flex: 1;
  width: 100%;
}

.dropdown {
  position: relative;
  display: inline-block;
  z-index: 1000;
}

.dropdown-content {
  position: absolute;
  top: calc(100% + 5px);
  left: 0;
  background-color: #fff;
  min-width: 280px;
  max-height: 400px;
  overflow-y: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  padding: 4px 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.dropdown-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 16px;
  color: #333;
  font-size: 14px;
  line-height: 1.5;
  cursor: pointer;
  transition: all 0.3s;
  background: transparent;
  border: none;
  width: 100%;
  margin: 0;
  gap: 12px;
}

.dropdown-item:hover {
  background-color: #f0f7ff;
}

.period-label {
  flex: 1;
  text-align: left;
  cursor: pointer;
  color: #333;
  transition: color 0.3s;
}

.period-label:hover {
  color: #1890ff;
}

.delete-button {
  color: #f5222d;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 3px;
  transition: all 0.3s;
}

.delete-button:hover {
  background-color: #fff1f0;
  color: #cf1322;
}

/* Loading and no data states */
.dropdown-content>div {
  padding: 12px 16px;
  color: #666;
  font-size: 14px;
  text-align: center;
}

/* Fade transition for smoother appearance */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.remark-dialog-mask {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remark-dialog {
  background: #fff;
  border-radius: 8px;
  padding: 24px 20px 16px 20px;
  min-width: 320px;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.18);
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.remark-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 8px;
}

.remark-input {
  width: 100%;
  min-height: 60px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 6px;
  font-size: 14px;
  resize: vertical;
}

.remark-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 8px;
}

.period-input-group {
  position: relative;
  display: flex;
  align-items: center;
  flex: 1;
  max-width: 320px;
}

.period-input-container {
  display: flex;
  align-items: center;
  width: 100%;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fff;
  transition: all 0.3s;
}

.period-input-container:hover {
  border-color: #40a9ff;
}

.period-input-container:focus-within {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.period-input {
  flex: 1;
  height: 32px;
  padding: 4px 11px;
  border: none;
  border-radius: 4px 0 0 4px;
  font-size: 13px;
  background-color: transparent;
  cursor: pointer;
  outline: none;
}

.period-input::placeholder {
  color: #bfbfbf;
}

/* 数据源选择弹窗样式 */
.data-source-dialog-mask {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(4px);
}

.data-source-dialog {
  background: #fff;
  border-radius: 12px;
  min-width: 480px;
  max-width: 600px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  animation: dialogSlideIn 0.3s ease-out;
}

@keyframes dialogSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.dialog-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
  line-height: 1;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.dialog-content {
  padding: 24px;
}

.data-source-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.radio-option {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 2px solid #e8e8e8;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  background: #fafafa;
}

.radio-option:hover {
  border-color: #40a9ff;
  background: #f0f7ff;
}

.radio-option.active {
  border-color: #1890ff;
  background: #e6f7ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.radio-input {
  display: none;
}

.radio-content {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.radio-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.radio-text {
  flex: 1;
}

.radio-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.radio-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.date-selection {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.date-inputs {
  display: flex;
  gap: 16px;
}

.input-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.date-select {
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  background: #fff;
  cursor: pointer;
  transition: all 0.3s;
}

.date-select:hover {
  border-color: #40a9ff;
}

.date-select:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

.dialog-button {
  padding: 8px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  min-width: 80px;
}

.dialog-button.cancel {
  background: #f5f5f5;
  color: #666;
}

.dialog-button.cancel:hover {
  background: #e8e8e8;
  color: #333;
}

.dialog-button.confirm {
  background: #1890ff;
  color: white;
}

.dialog-button.confirm:hover:not(:disabled) {
  background: #40a9ff;
}

.dialog-button.confirm:disabled {
  background: #d9d9d9;
  color: #999;
  cursor: not-allowed;
}

/* 滑入动画 */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.slide-down-enter-from,
.slide-down-leave-to {
  opacity: 0;
  max-height: 0;
  transform: translateY(-10px);
}

.slide-down-enter-to,
.slide-down-leave-from {
  opacity: 1;
  max-height: 200px;
  transform: translateY(0);
}
</style>