!function(t,e){var n=void 0!==t?t.fetch:"undefined"!=typeof self?self.fetch:null,r=null;!(t.navigator&&t.navigator.userAgent.toLowerCase().indexOf("ascdesktopeditor")<0)&&(t.location&&"file:"==t.location.protocol||t.document&&t.document.currentScript&&0==t.document.currentScript.src.indexOf("file:///"))?(n=e,r=function(){var t="ascdesktop://fonts/"+T.substr(8);return new Promise((function(e,n){var r=new XMLHttpRequest;r.open("GET",t,!0),r.responseType="arraybuffer",r.overrideMimeType?r.overrideMimeType("text/plain; charset=x-user-defined"):r.setRequestHeader("Accept-Charset","x-user-defined"),r.onload=function(){200==this.status&&e(new Uint8Array(this.response))},r.send(null)}))}):r=function(){return function(){if(!d&&(l||c)&&"function"==typeof n)return n(T,{credentials:"same-origin"}).then((function(t){if(!t.ok)throw"failed to load wasm binary file at '"+T+"'";return t.arrayBuffer()})).catch((function(){return O(T)}));return Promise.resolve().then((function(){return O(T)}))}()},e!==String.prototype.fromUtf8&&e!==String.prototype.toUtf8||(String.prototype.fromUtf8=function(t,n,r){e===n&&(n=0),e===r&&(r=t.length);for(var i="",o=n,a=n+r;o<a;){var s=t[o++];if(128&s){var u=63&t[o++];if(192!=(224&s)){var f=63&t[o++];if((s=224==(240&s)?(15&s)<<12|u<<6|f:(7&s)<<18|u<<12|f<<6|63&t[o++])<65536)i+=String.fromCharCode(s);else{var l=s-65536;i+=String.fromCharCode(55296|l>>10,56320|1023&l)}}else i+=String.fromCharCode((31&s)<<6|u)}else i+=String.fromCharCode(s)}return i},String.prototype.toUtf8=function(t){for(var e=this.length,n=new ArrayBuffer(6*e+1),r=0,i=0,o=0,a=new Uint8Array(n);i<e;)(r=this.charCodeAt(i++))>=55296&&r<=57343&&i<e&&(r=65536+((1023&r)<<10|1023&this.charCodeAt(i++))),r<128?a[o++]=r:r<2048?(a[o++]=192|r>>6,a[o++]=128|63&r):r<65536?(a[o++]=224|r>>12,a[o++]=128|r>>6&63,a[o++]=128|63&r):r<2097151?(a[o++]=240|r>>18,a[o++]=128|r>>12&63,a[o++]=128|r>>6&63,a[o++]=128|63&r):r<67108863?(a[o++]=248|r>>24,a[o++]=128|r>>18&63,a[o++]=128|r>>12&63,a[o++]=128|r>>6&63,a[o++]=128|63&r):r<2147483647&&(a[o++]=252|r>>30,a[o++]=128|r>>24&63,a[o++]=128|r>>18&63,a[o++]=128|r>>12&63,a[o++]=128|r>>6&63,a[o++]=128|63&r);return!0!==t&&(a[o++]=0),new Uint8Array(n,0,o)});var i,o=void 0!==o?o:{},a=Object.assign({},o),s=[],u="./this.program",f=(t,e)=>{throw e},l="object"==typeof t,c="function"==typeof importScripts,h=("object"==typeof process&&"object"==typeof process.versions&&process.versions.node,"");(l||c)&&(c?h=self.location.href:"undefined"!=typeof document&&document.currentScript&&(h=document.currentScript.src),h=0!==h.indexOf("blob:")?h.substr(0,h.replace(/[?#].*/,"").lastIndexOf("/")+1):"",t=>{var e=new XMLHttpRequest;return e.open("GET",t,!1),e.send(null),e.responseText},c&&(i=t=>{var e=new XMLHttpRequest;return e.open("GET",t,!1),e.responseType="arraybuffer",e.send(null),new Uint8Array(e.response)}),(t,e,n)=>{var r=new XMLHttpRequest;r.open("GET",t,!0),r.responseType="arraybuffer",r.onload=()=>{200==r.status||0==r.status&&r.response?e(r.response):n()},r.onerror=n,r.send(null)});var p=o.print||console.log.bind(console),_=o.printErr||console.warn.bind(console);Object.assign(o,a),a=null,o.arguments&&(s=o.arguments),o.thisProgram&&(u=o.thisProgram),o.quit&&(f=o.quit);var d,y=0;o.wasmBinary&&(d=o.wasmBinary);var m,g=o.noExitRuntime||!0;"object"!=typeof WebAssembly&&D("no native wasm support detected");var v,b,w,A,R=!1,E="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):e;function Z(t,e,n){for(var r=e+n,i=e;t[i]&&!(i>=r);)++i;if(i-e>16&&t.buffer&&E)return E.decode(t.subarray(e,i));for(var o="";e<i;){var a=t[e++];if(128&a){var s=63&t[e++];if(192!=(224&a)){var u=63&t[e++];if((a=224==(240&a)?(15&a)<<12|s<<6|u:(7&a)<<18|s<<12|u<<6|63&t[e++])<65536)o+=String.fromCharCode(a);else{var f=a-65536;o+=String.fromCharCode(55296|f>>10,56320|1023&f)}}else o+=String.fromCharCode((31&a)<<6|s)}else o+=String.fromCharCode(a)}return o}function F(t,e){return t?Z(w,t,e):""}function S(t){v=t,o.HEAP8=b=new Int8Array(t),o.HEAP16=new Int16Array(t),o.HEAP32=A=new Int32Array(t),o.HEAPU8=w=new Uint8Array(t),o.HEAPU16=new Uint16Array(t),o.HEAPU32=new Uint32Array(t),o.HEAPF32=new Float32Array(t),o.HEAPF64=new Float64Array(t)}o.INITIAL_MEMORY;var I,P=[],C=[],G=[function(){self.onZlibEngineInit()}];var H=0,M=null,U=null;function D(t){throw o.onAbort&&o.onAbort(t),_(t="Aborted("+t+")"),R=!0,1,t+=". Build with -s ASSERTIONS=1 for more info.",new WebAssembly.RuntimeError(t)}o.preloadedImages={},o.preloadedAudios={};var T,x,B="data:application/octet-stream;base64,";function L(t){return t.startsWith(B)}function O(t){try{if(t==T&&d)return new Uint8Array(d);if(i)return i(t);throw"both async and sync fetching of the wasm failed"}catch(t){D(t)}}function W(t){for(;t.length>0;){var n=t.shift();if("function"!=typeof n){var r=n.func;"number"==typeof r?n.arg===e?j(r)():j(r)(n.arg):r(n.arg===e?null:n.arg)}else n(o)}}L(T="zlib.wasm")||(x=T,T=o.locateFile?o.locateFile(x,h):h+x);var k=[];function j(t){var e=k[t];return e||(t>=k.length&&(k.length=t+1),k[t]=e=I.get(t)),e}function z(t){this.excPtr=t,this.ptr=t-16,this.set_type=function(t){A[this.ptr+4>>2]=t},this.get_type=function(){return A[this.ptr+4>>2]},this.set_destructor=function(t){A[this.ptr+8>>2]=t},this.get_destructor=function(){return A[this.ptr+8>>2]},this.set_refcount=function(t){A[this.ptr>>2]=t},this.set_caught=function(t){t=t?1:0,b[this.ptr+12>>0]=t},this.get_caught=function(){return 0!=b[this.ptr+12>>0]},this.set_rethrown=function(t){t=t?1:0,b[this.ptr+13>>0]=t},this.get_rethrown=function(){return 0!=b[this.ptr+13>>0]},this.init=function(t,e){this.set_type(t),this.set_destructor(e),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var t=A[this.ptr>>2];A[this.ptr>>2]=t+1},this.release_ref=function(){var t=A[this.ptr>>2];return A[this.ptr>>2]=t-1,1===t}}var q={buffers:[null,[],[]],printChar:function(t,e){var n=q.buffers[t];0===e||10===e?((1===t?p:_)(Z(n,0)),n.length=0):n.push(e)},varargs:e,get:function(){return q.varargs+=4,A[q.varargs-4>>2]},getStr:function(t){return F(t)},get64:function(t,e){return t}};function X(t){try{return m.grow(t-v.byteLength+65535>>>16),S(m.buffer),1}catch(t){}}var N={};function J(){if(!J.strings){var t={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:u||"./this.program"};for(var n in N)N[n]===e?delete t[n]:t[n]=N[n];var r=[];for(var n in t)r.push(n+"="+t[n]);J.strings=r}return J.strings}var Y,K={c:function(t,e,n,r){D("Assertion failed: "+F(t)+", at: "+[e?F(e):"unknown filename",n,r?F(r):"unknown function"])},d:function(t){return Q(t+16)+16},m:function(t,e,n){throw new z(t).init(e,n),t,t},p:function(t,e,n){return q.varargs=n,0},C:function(t,e,n){return q.varargs=n,0},D:function(t,e,n,r){q.varargs=r},x:function(t){},w:function(t,e){},y:function(t,e,n){},E:function(){return Date.now()},u:function(){throw 1/0},k:function(){D("")},F:function(t,e,n){w.copyWithin(t,e,e+n)},v:function(t){var e,n,r=w.length,i=2147483648;if((t>>>=0)>i)return!1;for(var o=1;o<=4;o*=2){var a=r*(1+.2/o);if(a=Math.min(a,t+100663296),X(Math.min(i,(e=Math.max(t,a))+((n=65536)-e%n)%n)))return!0}return!1},z:function(t,e){var n=0;return J().forEach((function(r,i){var o=e+n;A[t+4*i>>2]=o,function(t,e,n){for(var r=0;r<t.length;++r)b[e++>>0]=t.charCodeAt(r);n||(b[e>>0]=0)}(r,o),n+=r.length+1})),0},A:function(t,e){var n=J();A[t>>2]=n.length;var r=0;return n.forEach((function(t){r+=t.length+1})),A[e>>2]=r,0},G:function(t){!function(t,e){t,function(t){t,function(){return g}()||(o.onExit&&o.onExit(t),R=!0);f(t,new et(t))}(t)}(t)},q:function(t){return 0},B:function(t,e,n,r){var i=q.getStreamFromFD(t),o=q.doReadv(i,e,n);return A[r>>2]=o,0},t:function(t,e,n,r,i){},o:function(t,e,n,r){for(var i=0,o=0;o<n;o++){var a=A[e>>2],s=A[e+4>>2];e+=8;for(var u=0;u<s;u++)q.printChar(t,w[a+u]);i+=s}return A[r>>2]=i,0},a:function(){return y},h:function(t,e){var n=$();try{return j(t)(e)}catch(t){if(tt(n),t!==t+0)throw t;V(1,0)}},j:function(t,e,n){var r=$();try{return j(t)(e,n)}catch(t){if(tt(r),t!==t+0)throw t;V(1,0)}},f:function(t,e,n,r){var i=$();try{return j(t)(e,n,r)}catch(t){if(tt(i),t!==t+0)throw t;V(1,0)}},l:function(t,e,n,r,i){var o=$();try{return j(t)(e,n,r,i)}catch(t){if(tt(o),t!==t+0)throw t;V(1,0)}},n:function(t,e,n,r,i,o){var a=$();try{return j(t)(e,n,r,i,o)}catch(t){if(tt(a),t!==t+0)throw t;V(1,0)}},r:function(t){var e=$();try{j(t)()}catch(t){if(tt(e),t!==t+0)throw t;V(1,0)}},i:function(t,e){var n=$();try{j(t)(e)}catch(t){if(tt(n),t!==t+0)throw t;V(1,0)}},e:function(t,e,n){var r=$();try{j(t)(e,n)}catch(t){if(tt(r),t!==t+0)throw t;V(1,0)}},g:function(t,e,n,r){var i=$();try{j(t)(e,n,r)}catch(t){if(tt(i),t!==t+0)throw t;V(1,0)}},s:function(t,e,n,r,i){var o=$();try{j(t)(e,n,r,i)}catch(t){if(tt(o),t!==t+0)throw t;V(1,0)}},H:function(t,e,n,r,i,o,a,s,u,f){var l=$();try{j(t)(e,n,r,i,o,a,s,u,f)}catch(t){if(tt(l),t!==t+0)throw t;V(1,0)}},b:function(t){y=t}},Q=(function(){var t={a:K};function e(t,e){var n,r=t.exports;o.asm=r,S((m=o.asm.I).buffer),I=o.asm.M,n=o.asm.J,C.unshift(n),function(t){if(H--,o.monitorRunDependencies&&o.monitorRunDependencies(H),0==H&&(null!==M&&(clearInterval(M),M=null),U)){var e=U;U=null,e()}}()}function i(t){e(t.instance)}function a(e){return r().then((function(e){return WebAssembly.instantiate(e,t)})).then((function(t){return t})).then(e,(function(t){_("failed to asynchronously prepare wasm: "+t),D(t)}))}if(H++,o.monitorRunDependencies&&o.monitorRunDependencies(H),o.instantiateWasm)try{return o.instantiateWasm(t,e)}catch(t){return _("Module.instantiateWasm callback failed with error: "+t),!1}d||"function"!=typeof WebAssembly.instantiateStreaming||L(T)||"function"!=typeof n?a(i):n(T,{credentials:"same-origin"}).then((function(e){return WebAssembly.instantiateStreaming(e,t).then(i,(function(t){return _("wasm streaming compile failed: "+t),_("falling back to ArrayBuffer instantiation"),a(i)}))}))}(),o.___wasm_call_ctors=function(){return(o.___wasm_call_ctors=o.asm.J).apply(null,arguments)},o._malloc=function(){return(Q=o._malloc=o.asm.K).apply(null,arguments)}),V=(o._free=function(){return(o._free=o.asm.L).apply(null,arguments)},o._Zlib_Malloc=function(){return(o._Zlib_Malloc=o.asm.N).apply(null,arguments)},o._Zlib_Free=function(){return(o._Zlib_Free=o.asm.O).apply(null,arguments)},o._Zlib_Create=function(){return(o._Zlib_Create=o.asm.P).apply(null,arguments)},o._Zlib_Open=function(){return(o._Zlib_Open=o.asm.Q).apply(null,arguments)},o._Zlib_Close=function(){return(o._Zlib_Close=o.asm.R).apply(null,arguments)},o._Zlib_AddFile=function(){return(o._Zlib_AddFile=o.asm.S).apply(null,arguments)},o._Zlib_RemoveFile=function(){return(o._Zlib_RemoveFile=o.asm.T).apply(null,arguments)},o._Zlib_GetPaths=function(){return(o._Zlib_GetPaths=o.asm.U).apply(null,arguments)},o._Zlib_GetFile=function(){return(o._Zlib_GetFile=o.asm.V).apply(null,arguments)},o._Zlib_Save=function(){return(o._Zlib_Save=o.asm.W).apply(null,arguments)},o._Raster_DecodeFile=function(){return(o._Raster_DecodeFile=o.asm.X).apply(null,arguments)},o._Raster_GetDecodedBuffer=function(){return(o._Raster_GetDecodedBuffer=o.asm.Y).apply(null,arguments)},o._Raster_GetWidth=function(){return(o._Raster_GetWidth=o.asm.Z).apply(null,arguments)},o._Raster_GetHeight=function(){return(o._Raster_GetHeight=o.asm._).apply(null,arguments)},o._Raster_GetStride=function(){return(o._Raster_GetStride=o.asm.$).apply(null,arguments)},o._Raster_Destroy=function(){return(o._Raster_Destroy=o.asm.aa).apply(null,arguments)},o._Raster_EncodeImageData=function(){return(o._Raster_EncodeImageData=o.asm.ba).apply(null,arguments)},o._Raster_Encode=function(){return(o._Raster_Encode=o.asm.ca).apply(null,arguments)},o._Raster_GetEncodedSize=function(){return(o._Raster_GetEncodedSize=o.asm.da).apply(null,arguments)},o._Raster_GetEncodedBuffer=function(){return(o._Raster_GetEncodedBuffer=o.asm.ea).apply(null,arguments)},o._Raster_DestroyEncodedData=function(){return(o._Raster_DestroyEncodedData=o.asm.fa).apply(null,arguments)},o._Image_GetFormat=function(){return(o._Image_GetFormat=o.asm.ga).apply(null,arguments)},o._setThrew=function(){return(V=o._setThrew=o.asm.ha).apply(null,arguments)}),$=o.stackSave=function(){return($=o.stackSave=o.asm.ia).apply(null,arguments)},tt=o.stackRestore=function(){return(tt=o.stackRestore=o.asm.ja).apply(null,arguments)};function et(t){this.name="ExitStatus",this.message="Program terminated with exit("+t+")",this.status=t}function nt(t){function e(){Y||(Y=!0,o.calledRun=!0,R||(!0,W(C),o.onRuntimeInitialized&&o.onRuntimeInitialized(),function(){if(o.postRun)for("function"==typeof o.postRun&&(o.postRun=[o.postRun]);o.postRun.length;)t=o.postRun.shift(),G.unshift(t);var t;W(G)}()))}t=t||s,H>0||(!function(){if(o.preRun)for("function"==typeof o.preRun&&(o.preRun=[o.preRun]);o.preRun.length;)t=o.preRun.shift(),P.unshift(t);var t;W(P)}(),H>0||(o.setStatus?(o.setStatus("Running..."),setTimeout((function(){setTimeout((function(){o.setStatus("")}),1),e()}),1)):e()))}if(U=function t(){Y||nt(),Y||(U=t)},o.run=nt,o.preInit)for("function"==typeof o.preInit&&(o.preInit=[o.preInit]);o.preInit.length>0;)o.preInit.pop()();function rt(){this.engine=0,this.files={}}nt(),rt.prototype.isModuleInit=!1,rt.prototype.open=function(t){if(!this.isModuleInit)return!1;if(this.engine&&this.close(),!t)return!1;var n=e!==t.byteLength?new Uint8Array(t):t,r=n.length,i=o._Zlib_Malloc(r);if(0==i)return!1;if(o.HEAP8.set(n,i),this.engine=o._Zlib_Open(i,r),0==this.engine)return o._Zlib_Free(i),!1;var a=o._Zlib_GetPaths(this.engine);if(0==a)return o._Zlib_Close(this.engine),o._Zlib_Free(i),!1;var s=new Int32Array(o.HEAP8.buffer,a,4)[0];s-=4;for(var u=new Uint8Array(o.HEAP8.buffer,a+4,s),f=0;f<s;){var l=u[f]|u[f+1]<<8|u[f+2]<<16|u[f+3]<<24,c="".fromUtf8(u,f+=4,l);this.files[c]=null,f+=l}return o._Zlib_Free(i),o._Zlib_Free(a),!0},rt.prototype.create=function(){return!!this.isModuleInit&&(this.engine&&this.close(),this.engine=o._Zlib_Create(),!!this.engine)},rt.prototype.save=function(){if(!this.isModuleInit||!this.engine)return null;var t=o._Zlib_Save(this.engine);if(0==t)return null;var e=new Int32Array(o.HEAP8.buffer,t,4)[0];return new Uint8Array(o.HEAP8.buffer,t+4,e)},rt.prototype.getPaths=function(){var t=[];if(!this.files)return t;for(var e in this.files)this.files.hasOwnProperty(e)&&t.push(e);return t},rt.prototype.getFile=function(t){if(!this.isModuleInit||!this.engine)return null;if(e===this.files[t])return null;if(null!==this.files[t]){if(this.files[t].l>0)return new Uint8Array(o.HEAP8.buffer,this.files[t].p,this.files[t].l);var n=new Int32Array(o.HEAP8.buffer,this.files[t].p,4)[0];return new Uint8Array(o.HEAP8.buffer,this.files[t].p+4,n)}var r=t.toUtf8(),i=o._Zlib_Malloc(r.length);if(0==i)return null;o.HEAP8.set(r,i);var a=o._Zlib_GetFile(this.engine,i);if(0==a)return o._Zlib_Free(i),null;n=new Int32Array(o.HEAP8.buffer,a,4)[0];return o._Zlib_Free(i),this.files[t]={p:a,l:0},new Uint8Array(o.HEAP8.buffer,a+4,n)},rt.prototype.addFile=function(t,n){if(!this.isModuleInit||!this.engine)return!1;if(!n)return!1;e!==this.files[t]&&this.removeFile(t);var r=t.toUtf8(),i=o._Zlib_Malloc(r.length);if(0==i)return!1;o.HEAP8.set(r,i);var a=e!==n.byteLength?new Uint8Array(n):n,s=a.length,u=o._Zlib_Malloc(s);return 0==u?(o._Zlib_Free(i),!1):(o.HEAP8.set(a,u),o._Zlib_AddFile(this.engine,i,u,s),this.files[t]={p:u,l:s},o._Zlib_Free(i),!0)},rt.prototype.removeFile=function(t){if(!this.isModuleInit||!this.engine)return!1;if(e===this.files[t])return!1;var n=t.toUtf8(),r=o._Zlib_Malloc(n.length);return 0!=r&&(o.HEAP8.set(n,r),o._Zlib_RemoveFile(this.engine,r),this.files[t]&&this.files[t].p&&(o._Zlib_Free(this.files[t].p),delete this.files[t]),o._Zlib_Free(r),!0)},rt.prototype.close=function(){if(this.isModuleInit&&this.engine){for(var t in this.files)this.files[t]&&this.files[t].p&&o._Zlib_Free(this.files[t].p);this.files={},this.engine&&o._Zlib_Free(this.engine),this.engine=0}},rt.prototype.getImageType=function(t){let e=this.getFile(t);return o._Image_GetFormat(this.files[t].p+4,e.length)},rt.prototype.getImageAsFormat=function(t,e){let n=this.getFile(t),r=o._Raster_Encode(this.files[t].p+4,n.length,e),i=o._Raster_GetEncodedSize(r),a=o._Raster_GetEncodedBuffer(r),s=new Uint8Array(i);return s.set(new Uint8Array(o.HEAP8.buffer,a,i)),o._Raster_DestroyEncodedData(r),s},rt.prototype.getImageAsSvg=function(t){let e=this.getFile(t),n=o._Raster_Encode(this.files[t].p+4,e.length,24),r=o._Raster_GetEncodedSize(n),i=o._Raster_GetEncodedBuffer(n),a=String.prototype.fromUtf8(new Uint8Array(o.HEAP8.buffer,i,r));return o._Raster_DestroyEncodedData(n),a},rt.prototype.getImageBlob=function(t){let e=this.getImageType(t);if(10!=e&&21!=e)return new Blob([this.getFile(t)],{type:AscCommon.openXml.GetMimeType(AscCommon.GetFileExtension(t))});let n=this.getFile(t),r=o._Raster_Encode(this.files[t].p+4,n.length,24),i=o._Raster_GetEncodedSize(r),a=o._Raster_GetEncodedBuffer(r),s=new Blob([new Uint8Array(o.HEAP8.buffer,a,i)],{type:AscCommon.openXml.GetMimeType("svg")});return o._Raster_DestroyEncodedData(r),s},t.AscCommon=t.AscCommon||{},t.AscCommon.CZLibEngineJS=rt,t.onZlibEngineInit=function(){rt.prototype.isModuleInit=!0,t.ZLibModule_onLoad&&t.ZLibModule_onLoad()}}(window,void 0);