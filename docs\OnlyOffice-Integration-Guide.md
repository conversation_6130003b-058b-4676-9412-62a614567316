# OnlyOffice Spreadsheet 集成指南

## 概述

本项目已成功集成OnlyOffice Spreadsheet编辑器，提供了两种不同的集成方式：

1. **API集成方式** (`OnlyOfficeSpreadsheetView.vue`) - 使用OnlyOffice的JavaScript API
2. **iframe集成方式** (`OnlyOfficeIframeView.vue`) - 使用iframe嵌入编辑器

## 文件结构

```
public/
├── sdkjs/                    # OnlyOffice SDK核心文件
│   ├── cell/                 # 电子表格相关文件
│   ├── common/               # 公共资源
│   ├── slide/                # 演示文稿相关文件
│   └── word/                 # 文档编辑相关文件
├── web-apps/                 # OnlyOffice Web应用
│   ├── apps/
│   │   ├── spreadsheeteditor/  # 电子表格编辑器
│   │   ├── documenteditor/     # 文档编辑器
│   │   └── presentationeditor/ # 演示文稿编辑器
│   └── vendor/               # 第三方依赖
└── wasm/                     # WebAssembly文件
```

## 集成方式对比

| 特性 | API集成 | iframe集成 |
|------|---------|------------|
| 集成复杂度 | 高 | 低 |
| 自定义程度 | 高 | 中 |
| 性能 | 好 | 中 |
| 维护成本 | 高 | 低 |
| 推荐场景 | 深度定制 | 快速集成 |

## 使用方法

### 1. iframe集成方式（推荐）

这是最简单的集成方式，适合快速部署：

```vue
<template>
  <iframe
    src="/web-apps/apps/spreadsheeteditor/main/index.html"
    width="100%"
    height="600px"
    frameborder="0">
  </iframe>
</template>
```

**优点：**
- 集成简单，开箱即用
- 不需要复杂的配置
- 稳定性好

**缺点：**
- 自定义程度有限
- 与父页面交互较复杂

### 2. API集成方式

适合需要深度定制的场景：

```javascript
// 加载必要的资源
const loadOnlyOfficeResources = async () => {
  // 加载CSS
  const cssLink = document.createElement('link')
  cssLink.rel = 'stylesheet'
  cssLink.href = '/sdkjs/cell/css/main.css'
  document.head.appendChild(cssLink)
  
  // 加载JavaScript
  const script = document.createElement('script')
  script.src = '/sdkjs/cell/sdk-all-min.js'
  document.head.appendChild(script)
}

// 初始化编辑器
const initEditor = () => {
  const config = {
    "document": {
      "fileType": "xlsx",
      "key": "unique_document_key",
      "title": "新建电子表格",
      "permissions": {
        "edit": true,
        "download": true
      }
    },
    "documentType": "cell",
    "editorConfig": {
      "mode": "edit",
      "lang": "zh-CN"
    }
  }
  
  new DocsAPI.DocEditor("editor-container", config)
}
```

## 功能特性

### 核心功能
- ✅ 完整的Excel兼容性
- ✅ 支持公式计算
- ✅ 图表和数据透视表
- ✅ 多种文件格式导入/导出
- ✅ 实时协作编辑
- ✅ 丰富的格式化选项

### 支持的文件格式
- **导入：** xlsx, xls, ods, csv, txt
- **导出：** xlsx, xls, ods, csv, pdf

### 浏览器兼容性
- Chrome 49+
- Firefox 44+
- Safari 10+
- Edge 12+
- IE 11+

## 配置选项

### 基础配置
```javascript
const config = {
  "document": {
    "fileType": "xlsx",        // 文件类型
    "key": "document_key",     // 文档唯一标识
    "title": "文档标题",        // 文档标题
    "url": "document_url",     // 文档URL（可选）
    "permissions": {
      "edit": true,            // 是否可编辑
      "download": true,        // 是否可下载
      "print": true           // 是否可打印
    }
  },
  "documentType": "cell",      // 文档类型：cell/word/slide
  "editorConfig": {
    "mode": "edit",            // 模式：edit/view
    "lang": "zh-CN",           // 语言
    "customization": {
      "autosave": true,        // 自动保存
      "toolbar": true,         // 显示工具栏
      "statusBar": true,       // 显示状态栏
      "chat": false,           // 聊天功能
      "comments": true         // 评论功能
    }
  }
}
```

### 高级配置
```javascript
const advancedConfig = {
  "editorConfig": {
    "customization": {
      "compactToolbar": false,    // 紧凑工具栏
      "leftMenu": true,           // 左侧菜单
      "rightMenu": true,          // 右键菜单
      "zoom": 100,                // 缩放比例
      "toolbarNoTabs": false,     // 隐藏工具栏标签
      "toolbarHideFileName": false // 隐藏文件名
    },
    "plugins": {
      "autostart": [],            // 自动启动的插件
      "pluginsData": []           // 插件数据
    }
  }
}
```

## 最佳实践

### 1. 性能优化
- 按需加载OnlyOffice资源
- 使用CDN加速资源加载
- 合理设置缓存策略

### 2. 安全考虑
- 验证文档来源
- 限制文件上传大小
- 实施访问权限控制

### 3. 用户体验
- 提供加载状态提示
- 支持全屏模式
- 添加快捷键说明

## 故障排除

### 常见问题

1. **编辑器无法加载**
   - 检查文件路径是否正确
   - 确认所有资源文件都已部署
   - 查看浏览器控制台错误信息

2. **中文显示异常**
   - 确认语言设置为 `zh-CN`
   - 检查字体文件是否正确加载

3. **功能按钮不响应**
   - 检查权限配置
   - 确认API版本兼容性

### 调试技巧
```javascript
// 启用调试模式
window.AscDesktopEditor = {
  isDebugMode: true
}

// 监听编辑器事件
docEditor.attachEvent("onDocumentStateChange", function(event) {
  console.log("文档状态变化:", event)
})
```

## 扩展开发

### 自定义插件
OnlyOffice支持自定义插件开发，可以扩展编辑器功能：

```javascript
// 插件配置示例
const pluginConfig = {
  "editorConfig": {
    "plugins": {
      "autostart": ["asc.{plugin-guid}"],
      "pluginsData": [
        {
          "url": "/plugins/custom-plugin/",
          "guid": "asc.{plugin-guid}"
        }
      ]
    }
  }
}
```

### 事件处理
```javascript
// 监听文档保存事件
docEditor.attachEvent("onDocumentSave", function(event) {
  console.log("文档已保存")
})

// 监听用户操作事件
docEditor.attachEvent("onDocumentReady", function() {
  console.log("文档准备就绪")
})
```

## 总结

OnlyOffice提供了强大的在线办公功能，通过合理的集成方式可以为用户提供优秀的文档编辑体验。建议根据具体需求选择合适的集成方式：

- **快速原型或简单需求**：使用iframe集成
- **深度定制或复杂交互**：使用API集成

无论选择哪种方式，都要注意性能优化和用户体验的提升。
