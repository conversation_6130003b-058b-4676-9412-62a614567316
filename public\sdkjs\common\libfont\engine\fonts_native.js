!function(e,n){e.AscFonts=e.AscFonts||{};var r=e.AscFonts,t=CreateEmbedObject("CTextMeasurerEmbed");function o(){this.error=0}o.prototype.free=function(){};let _=new o,i=new o;function u(e){this.size=e,this.pointer=new Uint8Array(e)}i.count=0,r.CopyStreamToMemory=function(e,n){return e},r.GetUint8ArrayFromPointer=function(e,n){return e},u.prototype.getBuffer=function(){return this.pointer},u.prototype.free=function(){},u.prototype.set=function(e,n){this.pointer[e]=n},r.AllocString=function(e){return new u(e)},r.FT_CreateLibrary=function(e){return t.FT_Init(e)},r.FT_Done_Library=function(e){e&&t.FT_Free(e)},r.FT_Set_TrueType_HintProp=function(e,n){return t.FT_Set_TrueType_HintProp(e,n)},r.FT_Open_Face=function(e,n,r,o){return t.FT_Open_Face2(e,n,o)},r.FT_Done_Face=function(e){e&&t.FT_Free(e)},r.FT_SetCMapForCharCode=function(e,n){return t.FT_SetCMapForCharCode(e,n)},r.FT_GetKerningX=function(e,n,r){return t.FT_GetKerningX(e,n,r)},r.FT_GetFaceMaxAdvanceX=function(e){return t.FT_GetFaceMaxAdvanceX(e)},r.FT_Set_Transform=function(e,n,r,o,_){return t.FT_Set_Transform(e,n,r,o,_)},r.FT_Set_Char_Size=function(e,n,r,o,_){return t.FT_Set_Char_Size(e,n,r,o,_)},r.FT_GetFaceInfo=function(e,n){var r=t.FT_GetFaceInfo(e);return r?(_.error=0,n.init(r,0,r.length),_):(_.error=1,_)},r.FT_Load_Glyph=function(e,n,r){return t.FT_Load_Glyph(e,n,r)},r.FT_SetCMapForCharCode=function(e,n){return t.FT_SetCMapForCharCode(e,n)},r.FT_Get_Glyph_Measure_Params=function(e,n,r){var o=t.FT_Get_Glyph_Measure_Params(e,!!n);return o?(r.init(new Uint8Array(o,0,o.length)),i.count=r.readInt(),i.error=0,i):(i.error=1,i)},r.FT_Get_Glyph_Render_Params=function(e,n,r){var o=t.FT_Get_Glyph_Render_Params(e,n);return o?(_.error=0,r.init(o,0,o.length),_):(_.error=1,_)},r.FT_Get_Glyph_Render_Buffer=function(e,n){return t.FT_Get_Glyph_Render_Buffer(e,n)};let a={};r.HB_FontFree=function(e){e&&t.FT_Free(e)},r.HB_ShapeText=function(e,n,r,o,_,u,F){a[u]||(a[u]=t.HB_LanguageFromString()),e.GetHBFont()||e.SetHBFont(t.HB_FontMalloc());let c=t.HB_ShapeText(e.GetFace(),e.GetHBFont(),n.pointer,r,o,_,a[u]);if(!c)return i.error=1,i;F.init(new Uint8Array(c,0,c.length));let f=F.readUInt();F.readPointer64();return i.count=(f-12)/26,i.error=0,i},r.Hyphen_Init=function(){},r.Hyphen_Destroy=function(){},r.Hyphen_CheckDictionary=function(e){return t.Hyphen_IsDictionaryExist(e)},r.Hyphen_LoadDictionary=function(e,n){return!1},r.Hyphen_Word=function(e,n){let r=t.Hyphen_Word(e,n);return r||[]},r.onLoadModule(),r.onLoadModule()}(window);