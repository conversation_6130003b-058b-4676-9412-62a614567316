import { createRouter, createWebHistory } from 'vue-router'
import BudgetReportView from '../views/BudgetReportView.vue'
import SalaryTax2View from '../views/SalaryTax2View.vue'
import QuickQueryView from '../views/QuickQueryView.vue'
import QuickFillView from '../views/QuickFillView.vue'
import DataAnalysisView from '../views/DataAnalysisView.vue'
import CapitalFlowView from '../views/CapitalFlowView.vue'
import SamrtWorkerView from '../views/SamrtWorkerView.vue'
import SmartReconciliationView from '../views/SmartReconciliationView.vue'
import SettingsView from '../views/SettingsView.vue'
import ProjectReportView from '../views/ProjectReportView.vue'
import OnlyOfficeSpreadsheetView from '../views/OnlyOfficeSpreadsheetView.vue'
import OnlyOfficeIframeView from '../views/OnlyOfficeIframeView.vue'


const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/data-analysis'
    },
    {
      path: '/data-analysis',
      name: 'data-analysis',
      component: DataAnalysisView,
      meta: {
        title: '我的账面',
        icon: 'DataAnalysis'
      }
    },
    {
      path: '/budget-report',
      name: 'budget-report',
      component: BudgetReportView,
      meta: {
        title: '报表快算',
        icon: 'TrendCharts'
      }
    },
    // {
    //   path: '/salary-tax',
    //   name: 'salary-tax',
    //   component: SalaryTaxView,
    //   meta: {
    //     title: '薪酬个税',
    //     icon: 'Money'
    //   }
    // },
    {
      path: '/salary-tax',
      name: 'salary-tax',
      component: SalaryTax2View,
      meta: {
        title: '薪酬个税',
        icon: 'Money'
      }
    },
    {
      path: '/capital-flow',
      name: 'capital-flow',
      component: CapitalFlowView,
      meta: {
        title: '资金流动',
        icon: 'SwitchButton'
      }
    },
    //{
    //  path: '/one-click-report',
    //  name: 'one-click-report',
    //  component: OneClickReportView,
    //  meta: {
    //    title: '一键报告',
    //    icon: 'Document'
    //  }
   // },
    {
      path: '/project-report',
      name: 'project-report',
      component: ProjectReportView,
      meta: {
        title: '项目台账',
        icon: 'Folder'
      }
    },
    {
      path: '/samrt-worker',
      name: 'samrt-worker',
      component: SamrtWorkerView,
      meta: {
        title: '工人专区',
        icon: 'User'
      }
    },
    {
      path: '/smart-reconciliation',
      name: 'smart-reconciliation',
      component: SmartReconciliationView,
      meta: {
        title: '对账抵消',
        icon: 'Files'
      }
    },
    {
      path: '/voucher-query',
      name: 'voucher-query',
      component: () => import('../views/VoucherQueryView.vue'),
      meta: {
        title: '凭证查询',
        icon: 'Document'
      }
    },
    {
      path: '/quick-query',
      name: 'quick-query',
      component: QuickQueryView,
      meta: {
        title: '快速查询',
        icon: 'Search'
      }
    },
    {
      path: '/quick-fill',
      name: 'quick-fill',
      component: QuickFillView,
      meta: {
        title: '速填模板',
        icon: 'EditPen'
      }
    },

    {
      path: '/onlyoffice-spreadsheet',
      name: 'onlyoffice-spreadsheet',
      component: OnlyOfficeSpreadsheetView,
      meta: {
        title: 'OnlyOffice表格',
        icon: 'Grid'
      }
    },
    {
      path: '/onlyoffice-iframe',
      name: 'onlyoffice-iframe',
      component: OnlyOfficeIframeView,
      meta: {
        title: 'OnlyOffice(iframe)',
        icon: 'Document'
      }
    },
    {
      path: '/settings',
      name: 'settings',
      component: SettingsView,
      meta: {
        title: '系统设置',
        icon: 'Setting'
      }
    }
  ],
})

export default router
