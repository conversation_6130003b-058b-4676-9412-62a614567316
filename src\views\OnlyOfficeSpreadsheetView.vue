<template>
  <div class="onlyoffice-view">
    <div class="header">
      <h1>OnlyOffice Spreadsheet 演示</h1>
      <div class="controls">
        <el-button type="primary" @click="createNewDocument">新建文档</el-button>
        <el-button type="success" @click="loadSampleData">加载示例数据</el-button>
        <el-button type="warning" @click="saveDocument">保存文档</el-button>
        <el-button type="info" @click="exportDocument">导出Excel</el-button>
      </div>
    </div>
    
    <div class="editor-container">
      <div id="onlyoffice-editor" ref="editorContainer"></div>
    </div>
    
    <div class="status-bar">
      <span>状态: {{ editorStatus }}</span>
      <span>文档ID: {{ documentId }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const editorContainer = ref(null)
const editorStatus = ref('初始化中...')
const documentId = ref('')
let docEditor = null

// OnlyOffice 配置
const onlyOfficeConfig = {
  "document": {
    "fileType": "xlsx",
    "key": "",
    "title": "新建电子表格",
    "url": "",
    "permissions": {
      "edit": true,
      "download": true,
      "print": true
    }
  },
  "documentType": "cell",
  "editorConfig": {
    "mode": "edit",
    "lang": "zh-CN",
    "callbackUrl": "",
    "customization": {
      "autosave": true,
      "forcesave": false,
      "submitForm": false,
      "toolbar": true,
      "header": true,
      "compactToolbar": false,
      "leftMenu": true,
      "rightMenu": true,
      "statusBar": true,
      "chat": false,
      "comments": true,
      "zoom": 100,
      "compactHeader": false,
      "toolbarNoTabs": false,
      "toolbarHideFileName": false
    },
    "user": {
      "id": "user1",
      "name": "用户1"
    }
  },
  "width": "100%",
  "height": "600px",
  "type": "desktop"
}

// 初始化编辑器
const initEditor = () => {
  try {
    // 检查OnlyOffice API是否可用
    if (typeof DocsAPI === 'undefined') {
      editorStatus.value = '错误: OnlyOffice API未加载'
      ElMessage.error('OnlyOffice API未正确加载，请检查文件路径')
      return
    }

    // 生成文档ID和密钥
    documentId.value = generateDocumentId()
    onlyOfficeConfig.document.key = documentId.value
    onlyOfficeConfig.document.title = `电子表格_${new Date().toLocaleDateString()}`
    
    // 创建编辑器实例
    docEditor = new DocsAPI.DocEditor("onlyoffice-editor", onlyOfficeConfig)
    
    editorStatus.value = '编辑器已就绪'
    ElMessage.success('OnlyOffice编辑器初始化成功')
    
  } catch (error) {
    console.error('初始化编辑器失败:', error)
    editorStatus.value = '初始化失败'
    ElMessage.error('编辑器初始化失败: ' + error.message)
  }
}

// 生成文档ID
const generateDocumentId = () => {
  return 'doc_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

// 创建新文档
const createNewDocument = () => {
  if (docEditor) {
    // 销毁当前编辑器
    docEditor.destroyEditor()
  }
  
  // 重新初始化
  setTimeout(() => {
    initEditor()
  }, 100)
}

// 加载示例数据
const loadSampleData = () => {
  if (!docEditor) {
    ElMessage.warning('编辑器未初始化')
    return
  }
  
  try {
    // 通过API向编辑器插入数据
    const sampleData = [
      ['姓名', '部门', '职位', '薪资', '入职日期'],
      ['张三', '技术部', '前端工程师', 15000, '2023-01-15'],
      ['李四', '产品部', '产品经理', 18000, '2023-02-20'],
      ['王五', '设计部', 'UI设计师', 12000, '2023-03-10'],
      ['赵六', '技术部', '后端工程师', 16000, '2023-04-05']
    ]
    
    // 注意：实际的数据插入需要通过OnlyOffice的API方法
    // 这里只是示例，具体实现需要参考OnlyOffice文档
    ElMessage.success('示例数据加载完成')
    
  } catch (error) {
    console.error('加载示例数据失败:', error)
    ElMessage.error('加载示例数据失败')
  }
}

// 保存文档
const saveDocument = () => {
  if (!docEditor) {
    ElMessage.warning('编辑器未初始化')
    return
  }
  
  try {
    // 触发保存
    docEditor.downloadAs()
    ElMessage.success('文档保存成功')
    
  } catch (error) {
    console.error('保存文档失败:', error)
    ElMessage.error('保存文档失败')
  }
}

// 导出文档
const exportDocument = () => {
  if (!docEditor) {
    ElMessage.warning('编辑器未初始化')
    return
  }
  
  try {
    // 导出为Excel格式
    docEditor.downloadAs('xlsx')
    ElMessage.success('文档导出成功')
    
  } catch (error) {
    console.error('导出文档失败:', error)
    ElMessage.error('导出文档失败')
  }
}

// 动态加载OnlyOffice脚本和样式
const loadOnlyOfficeResources = () => {
  return new Promise((resolve, reject) => {
    // 检查是否已经加载
    if (typeof DocsAPI !== 'undefined') {
      resolve()
      return
    }

    // 加载CSS样式
    const cssLink = document.createElement('link')
    cssLink.rel = 'stylesheet'
    cssLink.href = '/sdkjs/cell/css/main.css'
    document.head.appendChild(cssLink)

    const appCssLink = document.createElement('link')
    appCssLink.rel = 'stylesheet'
    appCssLink.href = '/web-apps/apps/spreadsheeteditor/main/resources/css/app.css'
    document.head.appendChild(appCssLink)

    // 加载字体脚本
    const fontScript = document.createElement('script')
    fontScript.src = '/sdkjs/common/AllFonts.js'
    document.head.appendChild(fontScript)

    // 加载核心SDK
    const sdkScript = document.createElement('script')
    sdkScript.src = '/sdkjs/cell/sdk-all-min.js'
    document.head.appendChild(sdkScript)

    // 加载主应用脚本
    const appScript = document.createElement('script')
    appScript.src = '/web-apps/apps/spreadsheeteditor/main/app.js'
    appScript.onload = () => {
      editorStatus.value = 'OnlyOffice资源加载完成'
      resolve()
    }
    appScript.onerror = () => {
      editorStatus.value = 'OnlyOffice资源加载失败'
      reject(new Error('OnlyOffice资源加载失败'))
    }

    document.head.appendChild(appScript)
  })
}

// 组件挂载
onMounted(async () => {
  try {
    editorStatus.value = '加载OnlyOffice资源...'
    await loadOnlyOfficeResources()

    // 等待DOM更新和资源加载
    await new Promise(resolve => setTimeout(resolve, 1000))

    initEditor()

  } catch (error) {
    console.error('组件初始化失败:', error)
    editorStatus.value = '初始化失败'
    ElMessage.error('组件初始化失败: ' + error.message)
  }
})

// 组件卸载
onUnmounted(() => {
  if (docEditor) {
    try {
      docEditor.destroyEditor()
    } catch (error) {
      console.error('销毁编辑器失败:', error)
    }
  }
})
</script>

<style scoped>
.onlyoffice-view {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.header {
  background: white;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header h1 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 24px;
}

.controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.editor-container {
  flex: 1;
  padding: 20px;
  overflow: hidden;
}

#onlyoffice-editor {
  width: 100%;
  height: 100%;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.status-bar {
  background: #f8f9fa;
  padding: 10px 20px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    padding: 15px;
  }
  
  .header h1 {
    font-size: 20px;
    margin-bottom: 10px;
  }
  
  .controls {
    flex-direction: column;
  }
  
  .editor-container {
    padding: 10px;
  }
  
  .status-bar {
    flex-direction: column;
    gap: 5px;
  }
}
</style>
